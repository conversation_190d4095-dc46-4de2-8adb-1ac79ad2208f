-- Create business_subcategories table for many-to-many relationship
CREATE TABLE public.business_subcategories (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  business_id uuid NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
  subcategory_id uuid NOT NULL REFERENCES public.categories_sub(id) ON DELETE CASCADE,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  UNIQUE(business_id, subcategory_id)
);

-- Enable RLS
ALTER TABLE public.business_subcategories ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Business owners can manage their subcategories" 
ON public.business_subcategories 
FOR ALL
USING (business_id IN (
  SELECT id FROM public.businesses WHERE owner_id = auth.uid()
));

CREATE POLICY "Everyone can view business subcategories" 
ON public.business_subcategories 
FOR SELECT 
USING (true);

-- Create index for better performance
CREATE INDEX idx_business_subcategories_business_id ON public.business_subcategories(business_id);
CREATE INDEX idx_business_subcategories_subcategory_id ON public.business_subcategories(subcategory_id);