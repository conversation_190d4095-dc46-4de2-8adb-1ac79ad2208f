import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/auth/useAuth';
import { toast } from 'sonner';
import { SubscriptionService } from '@/services/subscriptionService';
import { 
  Subscription, 
  SubscriptionState, 
  SubscriptionPlanDetails 
} from '@/types/subscription';

export const useSubscription = () => {
  const { user } = useAuth();

  // State for subscription status
  const [subscriptionState, setSubscriptionState] = useState<SubscriptionState>({
    subscription: null,
    isLoading: true,
    error: null,
    hasActiveSubscription: false,
    planDetails: null,
  });

  // Check if user has an active subscription
  const checkSubscription = async () => {
    if (!user) {
      setSubscriptionState(prev => ({
        ...prev,
        isLoading: false,
        hasActiveSubscription: false,
      }));
      return;
    }
  
    try {
      setSubscriptionState(prev => ({ ...prev, isLoading: true, error: null }));

      // For now, we'll simulate a subscription check
      // In a real implementation, this would query the subscriptions table
     const { data, error } = await supabase
        .from('business_subscriptions')
        .select('*')
        //.eq('user_id', user.id) //RSL
        .in('status', ['active', 'trial'])
        .order('created_at', { ascending: false }) // newest first
        .limit(1)
        .maybeSingle(); // returns null if none, instead of throwing

        console.log("checkSubscription", data);
      
      
        if (error && error.code !== 'PGRST116') {
        // PGRST116 is "not found" error, which is fine
        throw error;
      }

      // Map database result to our interface
      let subscription: Subscription | null = null;
      if (data) {
        // Map database tier_type back to our plan_type
        const dbTierTypeMap: Record<string, string> = {
          'basic': 'basic',
          'professional': 'professional', // Map professional back to premium for UI
          'enterprise': 'enterprise'
        };

        subscription = {
          id: data.id,
          user_id: data.user_id,
          plan_type: dbTierTypeMap[data.tier_type] as any,
          status: data.status as any,
          start_date: data.started_at,
          end_date: data.expires_at || '',
          created_at: data.created_at,
          updated_at: data.updated_at,
          trial_end_date: data.expires_at,
          is_yearly: data.is_yearly,
        };
      }

      const hasActiveSubscription = subscription !== null && 
        (subscription.status === 'active' || subscription.status === 'trial') && 
        new Date(subscription.end_date) > new Date();

      const planDetails = subscription ? 
        await SubscriptionService.getSubscriptionPlan(subscription.plan_type) : null;

      setSubscriptionState({
        subscription,
        isLoading: false,
        error: null,
        hasActiveSubscription,
        planDetails,
      });

    } catch (error) {
      console.error('Error checking subscription:', error);
      setSubscriptionState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to check subscription status',
        hasActiveSubscription: false,
      }));
    }
  };

  // Create a real subscription entry in the database
  const createSubscription = async (planType: string, billingCycle: 'monthly' | 'yearly' = 'monthly', changeType?: 'upgrade' | 'downgrade' | 'new') => {
    if (!user) return;
    console.log("createSubscription", planType, billingCycle, changeType);
    try {
      setSubscriptionState(prev => ({ ...prev, isLoading: true }));

      // If this is an upgrade or downgrade, update the current subscription status first
      if (changeType && subscriptionState.subscription) {
        const newStatus = changeType === 'upgrade' ? 'upgraded' : 'downgraded';
        
        const { error: updateError } = await supabase
          .from('business_subscriptions')
          .update({ 
            status: newStatus,
            updated_at: new Date().toISOString()
          })
          .eq('id', subscriptionState.subscription.id)
          .eq('user_id', user.id);

        if (updateError) {
          throw updateError;
        }
      }

      const startDate = new Date();
      
      // Calculate end date based on billing cycle
      const endDate = new Date(startDate);
      if (billingCycle === 'yearly') {
        // Same day and month next year
        endDate.setFullYear(endDate.getFullYear() + 1);
      } else {
        // Same day next month
        endDate.setMonth(endDate.getMonth() + 1);
      }

      // Map our plan types to database tier_type enum
      const tierTypeMap: Record<string, 'basic' | 'professional' | 'enterprise'> = {
        'basic': 'basic',
        'professional': 'professional', // Map premium to professional in database
        'enterprise': 'enterprise'
      };

      const subscriptionData = {
        user_id: user.id,
        tier_type: tierTypeMap[planType] || 'basic',
        status: 'active',
        started_at: startDate.toISOString(),
        expires_at: endDate.toISOString(),
        is_yearly: billingCycle === 'yearly',
      };

      // Insert new subscription into database
      const { data, error } = await supabase
        .from('business_subscriptions')
        .insert(subscriptionData)
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Map database result back to our interface
      const newSubscription: Subscription = {
        id: data.id,
        user_id: data.user_id,
        plan_type: planType as any, // Keep our original plan type for UI
        status: data.status as any,
        start_date: data.started_at,
        end_date: data.expires_at || '',
        created_at: data.created_at,
        updated_at: data.updated_at,
       // trial_end_date: data.expires_at, // TODO: Add trial end date
      };

      const planDetails = await SubscriptionService.getSubscriptionPlan(planType);
      
      if (!planDetails) {
        throw new Error(`Plan details not found for ${planType}`);
      }
      
      setSubscriptionState({
        subscription: newSubscription,
        isLoading: false,
        error: null,
        hasActiveSubscription: true,
        planDetails,
      });

      const actionMessage = changeType === 'upgrade' ? 'aggiornata' : 
                           changeType === 'downgrade' ? 'declassata' : 'creata';
      toast.success(`Sottoscrizione ${actionMessage} a ${planDetails.name} con successo!`);
    } catch (error) {
      console.error('Error creating subscription:', error);
      setSubscriptionState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to create subscription',
      }));
      toast.error('Errore nella creazione della sottoscrizione');
    }
  };

  // Cancel subscription
  const cancelSubscription = async () => {
    if (!subscriptionState.subscription || !user) return;

    try {
      setSubscriptionState(prev => ({ ...prev, isLoading: true }));

      // Update subscription status in database
      const { error } = await supabase
        .from('business_subscriptions')
        .update({ 
          status: 'cancelled',
          updated_at: new Date().toISOString()
        })
        .eq('id', subscriptionState.subscription.id)
        .eq('user_id', user.id);

      if (error) {
        throw error;
      }

      // Update local state
      setSubscriptionState(prev => ({
        ...prev,
        subscription: prev.subscription ? {
          ...prev.subscription,
          status: 'cancelled'
        } : null,
        hasActiveSubscription: false,
        isLoading: false,
      }));

      toast.success('Sottoscrizione annullata con successo');
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      setSubscriptionState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to cancel subscription',
      }));
      toast.error('Errore nell\'annullamento della sottoscrizione');
    }
  };

  useEffect(() => {
    checkSubscription();
  }, [user]);

  return {
    ...subscriptionState,
    checkSubscription,
    createSubscription,
    cancelSubscription,
    refresh: checkSubscription,
  };
}; 