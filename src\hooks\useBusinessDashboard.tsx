
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { Business } from "@/hooks/useBusinesses";

export const useBusinessDashboard = (id: string | undefined) => {
  const navigate = useNavigate();
  const [business, setBusiness] = useState<Business | null>(null);
  const [dealsCount, setDealsCount] = useState<number>(0);
  const [draftDealsCount, setDraftDealsCount] = useState<number>(0);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState<Partial<Business>>({});
  const [isSaving, setIsSaving] = useState(false);

  const fetchBusiness = async () => {
    if (!id) return;

    console.log('Fetching business data...');
    try {
      const { data, error } = await supabase
        .from('businesses_with_counts')
        .select(`
          id, name, address, description, created_at, updated_at,
          latitude, longitude, photos, owner_id, category_id,
          email, phone, website, deal_count, booking_count, pending_booking_count
        `)
        .eq('id', id)
        .maybeSingle();

      if (error) {
        console.error('Error fetching business:', error);
        toast.error("Errore nel caricamento dell'attività");
        return;
      }

      if (!data) {
        console.error('No business found with id:', id);
        toast.error("Attività non trovata");
        navigate("/dashboard");
        return;
      }

      // Fetch subcategories separately
      const { data: subcategoryData, error: subcategoryError } = await supabase
        .from('business_subcategories')
        .select('subcategory_id')
        .eq('business_id', id);

      if (subcategoryError) {
        console.error('Error fetching subcategories:', subcategoryError);
      }

      const businessWithSubcategories = {
        ...data,
        subcategories: subcategoryData?.map(s => s.subcategory_id) || []
      } as Business;

      if (error) {
        console.error('Error fetching business:', error);
        toast.error("Errore nel caricamento dell'attività");
        return;
      }

      if (!data) {
        console.error('No business found with id:', id);
        toast.error("Attività non trovata");
        navigate("/dashboard");
        return;
      }

      console.log('Business data fetched:', businessWithSubcategories);
      setBusiness(businessWithSubcategories);
      setEditForm(businessWithSubcategories);
    } catch (error) {
      console.error('Error in fetchBusiness:', error);
      toast.error("Si è verificato un errore");
    }
  };

  const handleSave = async () => {
    if (!business || !id) return;

    setIsSaving(true);
    try {
      const removedPhotos = (business.photos || []).filter(
        photo => !(editForm.photos || []).includes(photo)
      );

      const updatedData = {
        name: editForm.name,
        description: editForm.description,
        address: editForm.address,
        latitude: editForm.latitude,
        longitude: editForm.longitude,
        phone: editForm.phone,
        email: editForm.email,
        website: editForm.website,
        photos: editForm.photos || [],
        category_id: editForm.category_id
      };

      // Update subcategories if changed
      if (editForm.subcategories !== undefined) {
        // Delete existing subcategories
        await supabase
          .from('business_subcategories')
          .delete()
          .eq('business_id', id);

        // Insert new subcategories if any
        if (editForm.subcategories.length > 0) {
          const subcategoryInserts = editForm.subcategories.map(subcategoryId => ({
            business_id: id,
            subcategory_id: subcategoryId
          }));

          const { error: subcategoryError } = await supabase
            .from('business_subcategories')
            .insert(subcategoryInserts);

          if (subcategoryError) {
            console.error('Error updating subcategories:', subcategoryError);
            toast.error("Errore durante l'aggiornamento delle sottocategorie");
          }
        }
      }

      console.log('Saving business data:', updatedData);
      console.log('Photos to remove:', removedPhotos);

      const { data, error } = await supabase
        .from('businesses')
        .update(updatedData)
        .eq('id', id)
        .select(`
          id, name, address, description, created_at, updated_at,
          latitude, longitude, photos, owner_id, category_id,
          email, phone, website
        `)
        .maybeSingle();

      if (error) {
        console.error('Error updating business:', error);
        throw error;
      }

      if (removedPhotos.length > 0) {
        console.log('Removing photos from storage...');
        const filesToRemove = removedPhotos.map(url => {
          const pathSegments = url.split('/');
          return `${pathSegments[pathSegments.length - 2]}/${pathSegments[pathSegments.length - 1]}`;
        });

        console.log('Files to remove:', filesToRemove);

        const { error: storageError } = await supabase.storage
          .from('business-photos')
          .remove(filesToRemove);

        if (storageError) {
          console.error('Error removing photos from storage:', storageError);
          toast.error("Errore durante la rimozione delle foto dal storage");
        } else {
          console.log('Photos removed from storage successfully');
        }
      }

      if (!data) {
        throw new Error('No data returned after update');
      }

      // Dopo l'aggiornamento, dobbiamo recuperare i conteggi aggiornati
      const { data: countData, error: countError } = await supabase
        .from('businesses_with_counts')
        .select(`deal_count, booking_count, pending_booking_count`)
        .eq('id', id)
        .maybeSingle();

      if (countError) {
        console.error('Error fetching updated counts:', countError);
      }

      // Aggiorniamo lo stato combinando i dati
      const updatedBusiness = {
        ...data,
        deal_count: countData?.deal_count || business.deal_count,
        booking_count: countData?.booking_count || business.booking_count,
        pending_booking_count: countData?.pending_booking_count || business.pending_booking_count,
        subcategories: editForm.subcategories || []
      } as Business;

      console.log('Business updated successfully:', updatedBusiness);

      setBusiness(updatedBusiness);
      setEditForm(updatedBusiness);
      setIsEditing(false);

      toast.success("Modifiche salvate con successo");
    } catch (error) {
      console.error('Errore durante il salvataggio:', error);
      toast.error("Errore durante il salvataggio delle modifiche");
    } finally {
      setIsSaving(false);
    }
  };

  const handleFormChange = (field: keyof Business, value: any) => {
    console.log(`Updating form field ${field}:`, value);
    setEditForm(prev => ({ ...prev, [field]: value }));
  };

  useEffect(() => {
    const fetchBusinessAndDeals = async () => {
      if (!id) {
        navigate("/dashboard");
        return;
      }

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        navigate("/login");
        return;
      }

      await fetchBusiness();

      // Count published deals
      const { count: publishedCount, error: publishedDealsError } = await supabase
        .from('deals')
        .select('*', { count: 'exact', head: true })
        .eq('business_id', id)
        .eq('status', 'published');

      if (publishedDealsError) {
        toast.error("Errore nel caricamento delle offerte pubblicate");
        return;
      }

      // Count draft deals
      const { count: draftCount, error: draftDealsError } = await supabase
        .from('deals')
        .select('*', { count: 'exact', head: true })
        .eq('business_id', id)
        .eq('status', 'draft');

      if (draftDealsError) {
        toast.error("Errore nel caricamento delle bozze");
        return;
      }

      setDealsCount(publishedCount || 0);
      setDraftDealsCount(draftCount || 0);
    };

    fetchBusinessAndDeals();
  }, [id, navigate]);

  return {
    business,
    dealsCount,
    draftDealsCount,
    currentImageIndex,
    isEditing,
    isSaving,
    editForm,
    setCurrentImageIndex,
    setIsEditing,
    setEditForm,
    handleSave,
    handleFormChange,
  };
};
