import { Arrow<PERSON><PERSON><PERSON>, Play, Pause, Check } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/hooks/auth/useAuth";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useTextToSpeechSample } from "@/hooks/useTextToSpeechSample";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import LoadingOverlay from "@/components/LoadingOverlay";

import { AIAssistantProfile } from "@/types/aiassistantprofile";
import { getAiAssistantProfilesQueryOptions } from "@/queryOptions/getAiAssistantProfilesQueryOptions";

// const fetchAssistants = async () => {
//   const { data, error } = await supabase
//     .from('ai_assistant_profile')
//     .select('*');

//   if (error) {
//     throw error;
//   }

//   return data;
// };

const AssistantSelect = () => {
  const navigate = useNavigate();
  const [selectedAssistant, setSelectedAssistant] = useState<string | null>(
    null
  );
  const [playingAssistantId, setPlayingAssistantId] = useState<string | null>(
    null
  );
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const { user, userDetails } = useAuth();

  // Track the current assistant (the one the user is already using)
  const currentAssistantId = userDetails?.selected_assistant_id;

  const queryClient = useQueryClient();
  const { playVoiceSample, stopPlayback } = useTextToSpeechSample();


  // TODO Use suspense pattern  https://www.youtube.com/watch?v=mPaCnwpFvZY
  const { data: assistants, isLoading: isLoadingAssistants } =  useQuery(getAiAssistantProfilesQueryOptions());

  useEffect(() => {
    const channel = supabase
      .channel("schema-db-changes")
      .on(
        "postgres_changes",
        {
          event: "*", // ascolta tutti gli eventi (INSERT, UPDATE, DELETE)
          schema: "public",
          table: "ai_assistant_profile",
        },
        () => {
          queryClient.invalidateQueries({
            queryKey: ["ai_assistant_profiles"],
          });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient]);

  const updateAssistantMutation = useMutation({
    mutationFn: async (assistantId: string) => {
      const { error } = await supabase
        .from("user_details")
        .update({ selected_assistant_id: assistantId })
        .eq("id", user?.id);

      if (error) throw error;
    },
    
    onSuccess: () => {
      toast.success("Assistente selezionato con successo");
      
      queryClient.invalidateQueries({ queryKey: ["userDetails"] });
      
      navigate("/profile");
    },

    onError: (error) => {
      console.error("Errore nella selezione dell'assistente:", error);
      toast.error("Errore nella selezione dell'assistente");
    },
  });

  useEffect(() => {
    if (userDetails?.selected_assistant_id) {
      setSelectedAssistant(userDetails.selected_assistant_id);
    }
  }, [userDetails]);

  // Check if the selected assistant is different from the current one
  const hasChanges = selectedAssistant !== currentAssistantId;

  // Helper function to get assistant name by ID
  const getAssistantName = (assistantId: string | null) => {
    if (!assistantId || !assistants) return "";
    return assistants.find(a => a.id === assistantId)?.name || "";
  };

  const handleSaveAssistant = () => {
    if (!user || !selectedAssistant) {
      toast.error("Errore nel salvare l'assistente selezionato");
      return;
    }

    // Show confirmation modal if there are changes
    if (hasChanges) {
      setShowConfirmationModal(true);
    }
  };

  const handleConfirmSave = () => {
    if (selectedAssistant) {
      updateAssistantMutation.mutate(selectedAssistant);
      setShowConfirmationModal(false);
    }
  };

  const handleCancelSave = () => {
    setShowConfirmationModal(false);
  };

  const handlePlayVoiceSample = (assistant: AIAssistantProfile) => {

    if (playingAssistantId === assistant.id) {
      stopPlayback();
      setPlayingAssistantId(null);
    } else {
      setPlayingAssistantId(assistant.id);
      playVoiceSample(
        assistant.voice_id,
        `Ciao, sono ${assistant.name}. Come posso aiutarti oggi?`
      );
    }
  };

  return (
    <div className="bg-white min-h-screen">
      <header className="fixed top-0 left-0 right-0 bg-white border-b border-gray-100 z-50">
        <div className="flex items-center justify-between px-4 py-3">
          <button onClick={() => navigate(-1)} className="text-gray-700">
            <ArrowLeft className="h-6 w-6" />
          </button>
          <h1 className="text-lg font-semibold">Scegli il tuo Assistente</h1>
          <div className="w-8"></div>
        </div>
      </header>

      <main className="pt-16 pb-24 px-4">
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">
            Seleziona il tuo Assistente Vocale
          </h2>
          <p className="text-gray-600">
            Scegli l&apos;assistente AI più adatto alle tue esigenze
          </p>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
          {isLoadingAssistants ? (
            <LoadingOverlay message="Caricamento assistenti..." />
          ) : (
            assistants?.map((assistant) => {
              const isCurrentAssistant = currentAssistantId === assistant.id;
              const isSelectedAssistant = selectedAssistant === assistant.id;

              return (
                <div
                  key={assistant.id}
                  className={`bg-white rounded-xl border overflow-hidden shadow-sm transition-all cursor-pointer relative ${
                    isSelectedAssistant
                      ? "border-blue-500 shadow-lg ring-2 ring-blue-200"
                      : isCurrentAssistant
                      ? "border-green-500 shadow-md ring-2 ring-green-200"
                      : "border-gray-200 hover:shadow-md hover:border-gray-300"
                  }`}
                  onClick={() => setSelectedAssistant(assistant.id)}
                >
                  {/* Current Assistant Indicator */}
                  {isCurrentAssistant && (
                    <div className="absolute top-2 left-2 z-10">
                      <div className="bg-green-500 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1">
                        <Check className="h-3 w-3" />
                        <span>Attuale</span>
                      </div>
                    </div>
                  )}

                  {/* Selected Assistant Indicator (only if different from current) */}
                  {isSelectedAssistant && !isCurrentAssistant && (
                    <div className="absolute top-2 left-2 z-10">
                      <div className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1">
                        <div className="h-3 w-3 rounded-full border-2 border-white bg-blue-500"></div>
                        <span>Selezionato</span>
                      </div>
                    </div>
                  )}

                  <div className="aspect-square bg-gray-100 relative">
                    <img
                      className="w-full h-full object-cover"
                      src={assistant.image_url || "/placeholder.svg"}
                      alt={`${assistant.name} assistente`}
                    />
                    <Button
                      onClick={(e) => {
                        e.stopPropagation();
                        handlePlayVoiceSample(assistant);
                      }}
                      className="absolute bottom-2 right-2 p-2 bg-white/80 backdrop-blur-sm  hover:bg-white transition-colors"
                    >
                      {playingAssistantId === assistant.id ? (
                        <Pause className="h-4 w-4 text-brand-primary animate-pulse" />
                      ) : (
                        <Play className="h-4 w-4 text-gray-700" />
                      )}
                    </Button>
                  </div>
                  <div className="p-3">
                    <h3 className="text-sm font-semibold mb-1">
                      {assistant.name}
                    </h3>
                    <p className="text-gray-600 text-xs line-clamp-2">
                      {assistant.description}
                    </p>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </main>

      <footer className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 p-4">
        <Button
          onClick={handleSaveAssistant}
          disabled={!selectedAssistant || !hasChanges || updateAssistantMutation.isPending}
          className="w-full bg-brand-primary text-white py-3 rounded-lg font-semibold hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {updateAssistantMutation.isPending ? "Salvataggio..." : "Salva"}
        </Button>
      </footer>

      {/* Confirmation Modal */}
      <AlertDialog open={showConfirmationModal} onOpenChange={setShowConfirmationModal}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Conferma Cambio Assistente</AlertDialogTitle>
            <AlertDialogDescription>
              Sei sicuro di voler cambiare il tuo assistente con{" "}
              <strong>{getAssistantName(selectedAssistant)}</strong>
              ? Questo diventerà il tuo nuovo assistente predefinito per tutte le conversazioni.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={handleCancelSave}
              disabled={updateAssistantMutation.isPending}
            >
              Annulla
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmSave}
              disabled={updateAssistantMutation.isPending}
              className="bg-brand-primary hover:bg-brand-primary/90"
            >
              {updateAssistantMutation.isPending ? "Salvataggio..." : "Conferma"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default AssistantSelect;
