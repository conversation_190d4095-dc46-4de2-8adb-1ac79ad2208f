import { useState, useEffect } from "react";
import { X, ChevronDown, Check } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { supabase } from "@/integrations/supabase/client";

interface Subcategory {
  id: string;
  name: string;
  category_id: string;
}

interface SubcategorySelectorProps {
  categoryId: string | null;
  selectedSubcategories: string[];
  onSubcategoriesChange: (subcategories: string[]) => void;
}

export const SubcategorySelector = ({
  categoryId,
  selectedSubcategories,
  onSubcategoriesChange,
}: SubcategorySelectorProps) => {
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (categoryId) {
      fetchSubcategories(categoryId);
    } else {
      setSubcategories([]);
      onSubcategoriesChange([]);
    }
  }, [categoryId]);

  const fetchSubcategories = async (catId: string) => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('categories_sub')
        .select('*')
        .eq('category_id', catId);

      if (error) throw error;
      setSubcategories(data || []);
    } catch (error) {
      console.error('Error fetching subcategories:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleSubcategory = (subcategoryId: string) => {
    const newSelection = selectedSubcategories.includes(subcategoryId)
      ? selectedSubcategories.filter(id => id !== subcategoryId)
      : [...selectedSubcategories, subcategoryId];
    
    onSubcategoriesChange(newSelection);
  };

  const removeSubcategory = (subcategoryId: string) => {
    onSubcategoriesChange(selectedSubcategories.filter(id => id !== subcategoryId));
  };

  const getSelectedSubcategoryNames = () => {
    return subcategories
      .filter(sub => selectedSubcategories.includes(sub.id))
      .map(sub => sub.name);
  };

  if (!categoryId) {
    return (
      <div className="text-sm text-muted-foreground">
        Seleziona prima una categoria principale per vedere le sottocategorie
      </div>
    );
  }

  if (loading) {
    return (
      <div className="text-sm text-muted-foreground">
        Caricamento sottocategorie...
      </div>
    );
  }

  if (subcategories.length === 0) {
    return (
      <div className="text-sm text-muted-foreground">
        Nessuna sottocategoria disponibile per questa categoria
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button 
            variant="outline" 
            className="w-full justify-between text-left"
            type="button"
          >
            <span className="text-muted-foreground">
              {selectedSubcategories.length === 0 
                ? "Seleziona sottocategorie" 
                : `${selectedSubcategories.length} sottocategorie selezionate`
              }
            </span>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-2" align="start">
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {subcategories.map((subcategory) => (
              <div
                key={subcategory.id}
                className="flex items-center space-x-2 p-2 hover:bg-muted rounded-md cursor-pointer"
                onClick={() => toggleSubcategory(subcategory.id)}
              >
                <Checkbox
                  checked={selectedSubcategories.includes(subcategory.id)}
                  onChange={() => {}} // Handled by parent onClick
                />
                <span className="text-sm flex-1">{subcategory.name}</span>
                {selectedSubcategories.includes(subcategory.id) && (
                  <Check className="h-4 w-4 text-primary" />
                )}
              </div>
            ))}
          </div>
        </PopoverContent>
      </Popover>

      {/* Selected Subcategories as Tags */}
      {selectedSubcategories.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {getSelectedSubcategoryNames().map((name, index) => {
            const subcategoryId = selectedSubcategories[index];
            return (
              <Badge
                key={subcategoryId}
                variant="secondary"
                className="flex items-center gap-1 px-2 py-1"
              >
                <span className="text-xs">{name}</span>
                <X
                  className="h-3 w-3 cursor-pointer hover:text-destructive"
                  onClick={() => removeSubcategory(subcategoryId)}
                />
              </Badge>
            );
          })}
        </div>
      )}
    </div>
  );
};