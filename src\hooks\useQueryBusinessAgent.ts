import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

/**
 * Business Agent type from Supabase
 */
export type BusinessAgent = Database["public"]["Tables"]["ai_business_agents"]["Row"];

/**
 * Hook to fetch business agent details for a specific business
 * Fetches the active booking agent for the business
 * 
 * @param businessId - The business ID to fetch agent for
 * @param enabled - Whether to enable the query (default: true when businessId exists)
 * @returns Query result with agent data, loading state, and error
 */
export const useQueryBusinessAgent = (businessId?: string, enabled = true) => {
  const {
    data: agent,
    isLoading,
    error: queryError,
  } = useQuery({
    queryKey: ['businessAgent', businessId],
    queryFn: async (): Promise<BusinessAgent | null> => {
      if (!businessId) return null;
      
      const { data, error: fetchError } = await supabase
        .from('ai_business_agents')
        .select('*')
        .eq('business_id', businessId)
        .eq('agent_type', 'booking')
        .eq('is_active', true)
        .maybeSingle(); // Use maybeSingle to handle case when no agent exists
      
      if (fetchError) {
        console.error('Error fetching business agent:', fetchError);
        throw new Error(fetchError.message);
      }
      
      return data as BusinessAgent || null;
    },
    enabled: enabled && !!businessId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 1,
  });

  // Convert React Query error to Error type for compatibility
  const error = queryError ? new Error(queryError.message || 'Unknown error') : null;

  return {
    agent: agent || null,
    isLoading,
    error,
    hasAgent: !!agent,
  };
};