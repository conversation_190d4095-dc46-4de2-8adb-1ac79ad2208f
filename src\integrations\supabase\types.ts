export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      agent_configurations: {
        Row: {
          agent_type: Database["public"]["Enums"]["agent_type_enum"]
          config: Json | null
          created_at: string | null
          description: string | null
          id: string
          is_active: boolean | null
          name: string
          system_prompt: string
          tools: Json | null
          updated_at: string | null
        }
        Insert: {
          agent_type: Database["public"]["Enums"]["agent_type_enum"]
          config?: Json | null
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          system_prompt: string
          tools?: Json | null
          updated_at?: string | null
        }
        Update: {
          agent_type?: Database["public"]["Enums"]["agent_type_enum"]
          config?: Json | null
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          system_prompt?: string
          tools?: Json | null
          updated_at?: string | null
        }
        Relationships: []
      }
      agent_conversations: {
        Row: {
          created_at: string | null
          current_agent_type:
            | Database["public"]["Enums"]["agent_type_enum"]
            | null
          id: string
          metadata: Json | null
          state: Json | null
          title: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          current_agent_type?:
            | Database["public"]["Enums"]["agent_type_enum"]
            | null
          id?: string
          metadata?: Json | null
          state?: Json | null
          title?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          current_agent_type?:
            | Database["public"]["Enums"]["agent_type_enum"]
            | null
          id?: string
          metadata?: Json | null
          state?: Json | null
          title?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "agent_conversations_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_messages: {
        Row: {
          agent_type: Database["public"]["Enums"]["agent_type_enum"] | null
          content: string
          conversation_id: string | null
          created_at: string | null
          id: string
          metadata: Json | null
          role: string
          tool_calls: Json | null
          tool_results: Json | null
        }
        Insert: {
          agent_type?: Database["public"]["Enums"]["agent_type_enum"] | null
          content: string
          conversation_id?: string | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          role: string
          tool_calls?: Json | null
          tool_results?: Json | null
        }
        Update: {
          agent_type?: Database["public"]["Enums"]["agent_type_enum"] | null
          content?: string
          conversation_id?: string | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          role?: string
          tool_calls?: Json | null
          tool_results?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "agent_messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "agent_conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_metrics: {
        Row: {
          agent_type: Database["public"]["Enums"]["agent_type_enum"]
          conversation_id: string | null
          created_at: string | null
          id: string
          metadata: Json | null
          metric_type: string
          value: number | null
        }
        Insert: {
          agent_type: Database["public"]["Enums"]["agent_type_enum"]
          conversation_id?: string | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          metric_type: string
          value?: number | null
        }
        Update: {
          agent_type?: Database["public"]["Enums"]["agent_type_enum"]
          conversation_id?: string | null
          created_at?: string | null
          id?: string
          metadata?: Json | null
          metric_type?: string
          value?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "agent_metrics_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "agent_conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      ai_assistant_profile: {
        Row: {
          assistant_id: string
          created_at: string
          description: string | null
          id: string
          image_url: string | null
          is_default: boolean
          name: string
          updated_at: string
          voice_id: string | null
        }
        Insert: {
          assistant_id: string
          created_at?: string
          description?: string | null
          id?: string
          image_url?: string | null
          is_default?: boolean
          name: string
          updated_at?: string
          voice_id?: string | null
        }
        Update: {
          assistant_id?: string
          created_at?: string
          description?: string | null
          id?: string
          image_url?: string | null
          is_default?: boolean
          name?: string
          updated_at?: string
          voice_id?: string | null
        }
        Relationships: []
      }
      ai_business_agents: {
        Row: {
          agent_type: Database["public"]["Enums"]["agent_type"]
          avatar_url: string | null
          business_id: string
          created_at: string
          description: string | null
          id: string
          instructions: string | null
          is_active: boolean
          name: string
          personality_style: string | null
          updated_at: string
          voice_id: string | null
          voice_settings: Json | null
        }
        Insert: {
          agent_type: Database["public"]["Enums"]["agent_type"]
          avatar_url?: string | null
          business_id: string
          created_at?: string
          description?: string | null
          id?: string
          instructions?: string | null
          is_active?: boolean
          name: string
          personality_style?: string | null
          updated_at?: string
          voice_id?: string | null
          voice_settings?: Json | null
        }
        Update: {
          agent_type?: Database["public"]["Enums"]["agent_type"]
          avatar_url?: string | null
          business_id?: string
          created_at?: string
          description?: string | null
          id?: string
          instructions?: string | null
          is_active?: boolean
          name?: string
          personality_style?: string | null
          updated_at?: string
          voice_id?: string | null
          voice_settings?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "business_ai_agents_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "business_ai_agents_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses_with_counts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "business_ai_agents_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "mv_business_availability"
            referencedColumns: ["business_id"]
          },
        ]
      }
      ai_business_agents_profile: {
        Row: {
          agent_type: Database["public"]["Enums"]["agent_type"]
          assistant_id: string
          avatar_url: string | null
          created_at: string
          description: string | null
          id: string
          name: string
          updated_at: string
          voice_id: string | null
        }
        Insert: {
          agent_type?: Database["public"]["Enums"]["agent_type"]
          assistant_id: string
          avatar_url?: string | null
          created_at?: string
          description?: string | null
          id?: string
          name: string
          updated_at?: string
          voice_id?: string | null
        }
        Update: {
          agent_type?: Database["public"]["Enums"]["agent_type"]
          assistant_id?: string
          avatar_url?: string | null
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          updated_at?: string
          voice_id?: string | null
        }
        Relationships: []
      }
      app_version: {
        Row: {
          created_at: string
          date: string | null
          id: number
          major: number | null
          minor: number | null
          notes: string | null
          patch: number | null
        }
        Insert: {
          created_at?: string
          date?: string | null
          id?: number
          major?: number | null
          minor?: number | null
          notes?: string | null
          patch?: number | null
        }
        Update: {
          created_at?: string
          date?: string | null
          id?: number
          major?: number | null
          minor?: number | null
          notes?: string | null
          patch?: number | null
        }
        Relationships: []
      }
      bookings: {
        Row: {
          booking_date: string
          booking_end_time: string
          booking_time: string
          cancellation_note: string | null
          created_at: string
          deal_id: string
          discount_percentage: number
          discounted_price: number
          fake: boolean
          id: string
          original_price: number
          qr_data: Json
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          booking_date: string
          booking_end_time?: string
          booking_time: string
          cancellation_note?: string | null
          created_at?: string
          deal_id: string
          discount_percentage: number
          discounted_price: number
          fake?: boolean
          id?: string
          original_price: number
          qr_data: Json
          status?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          booking_date?: string
          booking_end_time?: string
          booking_time?: string
          cancellation_note?: string | null
          created_at?: string
          deal_id?: string
          discount_percentage?: number
          discounted_price?: number
          fake?: boolean
          id?: string
          original_price?: number
          qr_data?: Json
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "bookings_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_dealid_view"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "bookings_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_enriched"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "bookings_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_with_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      business_product_categories: {
        Row: {
          business_id: string
          created_at: string
          description: string | null
          icon: string | null
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          business_id: string
          created_at?: string
          description?: string | null
          icon?: string | null
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          business_id?: string
          created_at?: string
          description?: string | null
          icon?: string | null
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "business_product_categories_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "business_product_categories_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses_with_counts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "business_product_categories_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "mv_business_availability"
            referencedColumns: ["business_id"]
          },
        ]
      }
      business_products: {
        Row: {
          business_id: string
          category: string | null
          category_text_old: string | null
          created_at: string
          description: string | null
          id: string
          images: string[] | null
          is_available: boolean | null
          name: string
          price: number | null
          sku: string | null
          stock_quantity: number | null
          updated_at: string
        }
        Insert: {
          business_id: string
          category?: string | null
          category_text_old?: string | null
          created_at?: string
          description?: string | null
          id?: string
          images?: string[] | null
          is_available?: boolean | null
          name: string
          price?: number | null
          sku?: string | null
          stock_quantity?: number | null
          updated_at?: string
        }
        Update: {
          business_id?: string
          category?: string | null
          category_text_old?: string | null
          created_at?: string
          description?: string | null
          id?: string
          images?: string[] | null
          is_available?: boolean | null
          name?: string
          price?: number | null
          sku?: string | null
          stock_quantity?: number | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "business_products_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "business_products_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses_with_counts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "business_products_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "mv_business_availability"
            referencedColumns: ["business_id"]
          },
          {
            foreignKeyName: "fk_business_products_category"
            columns: ["category"]
            isOneToOne: false
            referencedRelation: "business_product_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      business_subcategories: {
        Row: {
          business_id: string
          created_at: string
          id: string
          subcategory_id: string
        }
        Insert: {
          business_id: string
          created_at?: string
          id?: string
          subcategory_id: string
        }
        Update: {
          business_id?: string
          created_at?: string
          id?: string
          subcategory_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "business_subcategories_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "business_subcategories_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses_with_counts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "business_subcategories_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "mv_business_availability"
            referencedColumns: ["business_id"]
          },
          {
            foreignKeyName: "business_subcategories_subcategory_id_fkey"
            columns: ["subcategory_id"]
            isOneToOne: false
            referencedRelation: "categories_sub"
            referencedColumns: ["id"]
          },
        ]
      }
      business_subscriptions: {
        Row: {
          created_at: string
          expires_at: string | null
          id: string
          is_yearly: boolean
          started_at: string
          status: string
          tier_type: Database["public"]["Enums"]["pricing_tier_type"]
          trial_end_date: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          expires_at?: string | null
          id?: string
          is_yearly?: boolean
          started_at?: string
          status?: string
          tier_type: Database["public"]["Enums"]["pricing_tier_type"]
          trial_end_date?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          expires_at?: string | null
          id?: string
          is_yearly?: boolean
          started_at?: string
          status?: string
          tier_type?: Database["public"]["Enums"]["pricing_tier_type"]
          trial_end_date?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "business_subscriptions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      businesses: {
        Row: {
          address: string | null
          category_id: string | null
          city: string | null
          compliance_notes: string | null
          country: string | null
          created_at: string
          description: string | null
          email: string | null
          fake: boolean
          geom: unknown | null
          id: string
          latitude: number | null
          layout: Json | null
          legal_structure: string | null
          longitude: number | null
          name: string
          owner_id: string | null
          phone: string | null
          photos: string[] | null
          registration_number: string | null
          review_count: number | null
          score: number | null
          source: string
          state: string | null
          tax_id: string | null
          updated_at: string
          website: string | null
          zip_code: string | null
        }
        Insert: {
          address?: string | null
          category_id?: string | null
          city?: string | null
          compliance_notes?: string | null
          country?: string | null
          created_at?: string
          description?: string | null
          email?: string | null
          fake?: boolean
          geom?: unknown | null
          id?: string
          latitude?: number | null
          layout?: Json | null
          legal_structure?: string | null
          longitude?: number | null
          name: string
          owner_id?: string | null
          phone?: string | null
          photos?: string[] | null
          registration_number?: string | null
          review_count?: number | null
          score?: number | null
          source?: string
          state?: string | null
          tax_id?: string | null
          updated_at?: string
          website?: string | null
          zip_code?: string | null
        }
        Update: {
          address?: string | null
          category_id?: string | null
          city?: string | null
          compliance_notes?: string | null
          country?: string | null
          created_at?: string
          description?: string | null
          email?: string | null
          fake?: boolean
          geom?: unknown | null
          id?: string
          latitude?: number | null
          layout?: Json | null
          legal_structure?: string | null
          longitude?: number | null
          name?: string
          owner_id?: string | null
          phone?: string | null
          photos?: string[] | null
          registration_number?: string | null
          review_count?: number | null
          score?: number | null
          source?: string
          state?: string | null
          tax_id?: string | null
          updated_at?: string
          website?: string | null
          zip_code?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "businesses_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "businesses_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "deals_dealid_view"
            referencedColumns: ["category_id"]
          },
          {
            foreignKeyName: "businesses_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "deals_enriched"
            referencedColumns: ["category_id"]
          },
          {
            foreignKeyName: "businesses_owner_id_fkey"
            columns: ["owner_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_users: {
        Row: {
          campaign_id: string
          created_at: string
          id: string
          user_id: string
        }
        Insert: {
          campaign_id: string
          created_at?: string
          id?: string
          user_id: string
        }
        Update: {
          campaign_id?: string
          created_at?: string
          id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "campaign_users_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_users_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_waitlist: {
        Row: {
          campaign_id: string
          company: string | null
          created_at: string | null
          email: string
          email_sent: boolean | null
          email_sent_at: string | null
          first_name: string
          id: string
          interests: string | null
          isbusiness: boolean | null
          last_name: string
          linkedin: string | null
          marketing_consent: boolean | null
          phone: string | null
          role: string | null
          telegram: string | null
          updated_at: string | null
          whatsapp: string | null
        }
        Insert: {
          campaign_id: string
          company?: string | null
          created_at?: string | null
          email: string
          email_sent?: boolean | null
          email_sent_at?: string | null
          first_name: string
          id?: string
          interests?: string | null
          isbusiness?: boolean | null
          last_name: string
          linkedin?: string | null
          marketing_consent?: boolean | null
          phone?: string | null
          role?: string | null
          telegram?: string | null
          updated_at?: string | null
          whatsapp?: string | null
        }
        Update: {
          campaign_id?: string
          company?: string | null
          created_at?: string | null
          email?: string
          email_sent?: boolean | null
          email_sent_at?: string | null
          first_name?: string
          id?: string
          interests?: string | null
          isbusiness?: boolean | null
          last_name?: string
          linkedin?: string | null
          marketing_consent?: boolean | null
          phone?: string | null
          role?: string | null
          telegram?: string | null
          updated_at?: string | null
          whatsapp?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "campaign_waitlist_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
        ]
      }
      campaigns: {
        Row: {
          created_at: string
          description: string | null
          email_content: string | null
          email_subject: string | null
          id: string
          name: string
          template_id: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          email_content?: string | null
          email_subject?: string | null
          id?: string
          name: string
          template_id?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          email_content?: string | null
          email_subject?: string | null
          id?: string
          name?: string
          template_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "campaigns_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "email_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      categories: {
        Row: {
          created_at: string
          description: string | null
          icon: string | null
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          icon?: string | null
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          icon?: string | null
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      categories_sub: {
        Row: {
          category_id: string
          created_at: string
          description: string | null
          icon: string | null
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          category_id: string
          created_at?: string
          description?: string | null
          icon?: string | null
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          category_id?: string
          created_at?: string
          description?: string | null
          icon?: string | null
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_categories_sub_category_id"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_categories_sub_category_id"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "deals_dealid_view"
            referencedColumns: ["category_id"]
          },
          {
            foreignKeyName: "fk_categories_sub_category_id"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "deals_enriched"
            referencedColumns: ["category_id"]
          },
        ]
      }
      conversation_participants: {
        Row: {
          conversation_id: string
          joined_at: string
          role: Database["public"]["Enums"]["conversation_role"]
          user_id: string
        }
        Insert: {
          conversation_id: string
          joined_at?: string
          role: Database["public"]["Enums"]["conversation_role"]
          user_id: string
        }
        Update: {
          conversation_id?: string
          joined_at?: string
          role?: Database["public"]["Enums"]["conversation_role"]
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "conversation_participants_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "conversation_participants_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      conversations: {
        Row: {
          booking_id: string | null
          business_id: string | null
          created_at: string
          created_by: string
          deal_id: string | null
          id: string
          is_active: boolean
          last_message_at: string
          type: Database["public"]["Enums"]["conversation_type"]
          ui: string | null
          updated_at: string
        }
        Insert: {
          booking_id?: string | null
          business_id?: string | null
          created_at?: string
          created_by: string
          deal_id?: string | null
          id?: string
          is_active?: boolean
          last_message_at?: string
          type: Database["public"]["Enums"]["conversation_type"]
          ui?: string | null
          updated_at?: string
        }
        Update: {
          booking_id?: string | null
          business_id?: string | null
          created_at?: string
          created_by?: string
          deal_id?: string | null
          id?: string
          is_active?: boolean
          last_message_at?: string
          type?: Database["public"]["Enums"]["conversation_type"]
          ui?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "conversations_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "booking_with_all_details"
            referencedColumns: ["booking_id"]
          },
          {
            foreignKeyName: "conversations_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "bookings"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "conversations_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "conversations_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses_with_counts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "conversations_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "mv_business_availability"
            referencedColumns: ["business_id"]
          },
          {
            foreignKeyName: "conversations_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "booking_with_all_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "conversations_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "messages_with_user_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "conversations_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "conversations_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "conversations_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_dealid_view"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "conversations_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_enriched"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "conversations_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_with_info"
            referencedColumns: ["id"]
          },
        ]
      }
      conversations_thread: {
        Row: {
          conversation_id: string
          created_at: string
          id: string
          thread_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          conversation_id: string
          created_at?: string
          id?: string
          thread_id: string
          updated_at?: string
          user_id?: string
        }
        Update: {
          conversation_id?: string
          created_at?: string
          id?: string
          thread_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "conversations_thread_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "conversations_thread_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      coordinated_bookings: {
        Row: {
          booking_id: string | null
          created_at: string
          deal_id: string
          id: string
          plan_id: string
          scheduled_time: string | null
          service_order: number
          status: string
          travel_time_minutes: number | null
        }
        Insert: {
          booking_id?: string | null
          created_at?: string
          deal_id: string
          id?: string
          plan_id: string
          scheduled_time?: string | null
          service_order: number
          status?: string
          travel_time_minutes?: number | null
        }
        Update: {
          booking_id?: string | null
          created_at?: string
          deal_id?: string
          id?: string
          plan_id?: string
          scheduled_time?: string | null
          service_order?: number
          status?: string
          travel_time_minutes?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "coordinated_bookings_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "booking_with_all_details"
            referencedColumns: ["booking_id"]
          },
          {
            foreignKeyName: "coordinated_bookings_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "bookings"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "coordinated_bookings_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "coordinated_bookings_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_dealid_view"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "coordinated_bookings_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_enriched"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "coordinated_bookings_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_with_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "coordinated_bookings_plan_id_fkey"
            columns: ["plan_id"]
            isOneToOne: false
            referencedRelation: "multi_service_plans"
            referencedColumns: ["id"]
          },
        ]
      }
      dashboard_configurations: {
        Row: {
          business_id: string
          config: Json
          created_at: string | null
          id: string
          updated_at: string | null
        }
        Insert: {
          business_id: string
          config?: Json
          created_at?: string | null
          id?: string
          updated_at?: string | null
        }
        Update: {
          business_id?: string
          config?: Json
          created_at?: string | null
          id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "dashboard_configurations_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: true
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "dashboard_configurations_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: true
            referencedRelation: "businesses_with_counts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "dashboard_configurations_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: true
            referencedRelation: "mv_business_availability"
            referencedColumns: ["business_id"]
          },
        ]
      }
      deal_categories: {
        Row: {
          category_id: string
          created_at: string
          id: string
          name: string
        }
        Insert: {
          category_id: string
          created_at?: string
          id?: string
          name: string
        }
        Update: {
          category_id?: string
          created_at?: string
          id?: string
          name?: string
        }
        Relationships: [
          {
            foreignKeyName: "deal_categories_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deal_categories_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "deals_dealid_view"
            referencedColumns: ["category_id"]
          },
          {
            foreignKeyName: "deal_categories_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "deals_enriched"
            referencedColumns: ["category_id"]
          },
        ]
      }
      deal_embeddings: {
        Row: {
          content: string
          created_at: string
          deal_id: string
          embedding: string
          id: string
          updated_at: string
        }
        Insert: {
          content: string
          created_at?: string
          deal_id: string
          embedding: string
          id?: string
          updated_at?: string
        }
        Update: {
          content?: string
          created_at?: string
          deal_id?: string
          embedding?: string
          id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "deal_embeddings_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deal_embeddings_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_dealid_view"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "deal_embeddings_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_enriched"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "deal_embeddings_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_with_info"
            referencedColumns: ["id"]
          },
        ]
      }
      deal_relationships: {
        Row: {
          created_at: string
          discount_percentage: number | null
          id: string
          primary_deal_id: string
          priority: number | null
          related_deal_id: string
          relationship_type: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          discount_percentage?: number | null
          id?: string
          primary_deal_id: string
          priority?: number | null
          related_deal_id: string
          relationship_type: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          discount_percentage?: number | null
          id?: string
          primary_deal_id?: string
          priority?: number | null
          related_deal_id?: string
          relationship_type?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "deal_relationships_primary_deal_id_fkey"
            columns: ["primary_deal_id"]
            isOneToOne: false
            referencedRelation: "deals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deal_relationships_primary_deal_id_fkey"
            columns: ["primary_deal_id"]
            isOneToOne: false
            referencedRelation: "deals_dealid_view"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "deal_relationships_primary_deal_id_fkey"
            columns: ["primary_deal_id"]
            isOneToOne: false
            referencedRelation: "deals_enriched"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "deal_relationships_primary_deal_id_fkey"
            columns: ["primary_deal_id"]
            isOneToOne: false
            referencedRelation: "deals_with_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deal_relationships_related_deal_id_fkey"
            columns: ["related_deal_id"]
            isOneToOne: false
            referencedRelation: "deals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deal_relationships_related_deal_id_fkey"
            columns: ["related_deal_id"]
            isOneToOne: false
            referencedRelation: "deals_dealid_view"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "deal_relationships_related_deal_id_fkey"
            columns: ["related_deal_id"]
            isOneToOne: false
            referencedRelation: "deals_enriched"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "deal_relationships_related_deal_id_fkey"
            columns: ["related_deal_id"]
            isOneToOne: false
            referencedRelation: "deals_with_info"
            referencedColumns: ["id"]
          },
        ]
      }
      deals: {
        Row: {
          auto_confirm: boolean
          bundle_discount_percentage: number | null
          business_id: string
          category_id: string | null
          created_at: string
          description: string | null
          discount_percentage: number | null
          discounted_price: number
          end_date: string
          fake: boolean
          id: string
          images: string[] | null
          is_bundle: boolean | null
          original_price: number
          start_date: string
          status: Database["public"]["Enums"]["deal_status_enum"]
          terms_conditions: string
          time_slots: Json | null
          title: string
          updated_at: string
        }
        Insert: {
          auto_confirm?: boolean
          bundle_discount_percentage?: number | null
          business_id: string
          category_id?: string | null
          created_at?: string
          description?: string | null
          discount_percentage?: number | null
          discounted_price: number
          end_date: string
          fake?: boolean
          id?: string
          images?: string[] | null
          is_bundle?: boolean | null
          original_price: number
          start_date: string
          status?: Database["public"]["Enums"]["deal_status_enum"]
          terms_conditions?: string
          time_slots?: Json | null
          title: string
          updated_at?: string
        }
        Update: {
          auto_confirm?: boolean
          bundle_discount_percentage?: number | null
          business_id?: string
          category_id?: string | null
          created_at?: string
          description?: string | null
          discount_percentage?: number | null
          discounted_price?: number
          end_date?: string
          fake?: boolean
          id?: string
          images?: string[] | null
          is_bundle?: boolean | null
          original_price?: number
          start_date?: string
          status?: Database["public"]["Enums"]["deal_status_enum"]
          terms_conditions?: string
          time_slots?: Json | null
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "deals_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deals_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses_with_counts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deals_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "mv_business_availability"
            referencedColumns: ["business_id"]
          },
          {
            foreignKeyName: "deals_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deals_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "deals_dealid_view"
            referencedColumns: ["category_id"]
          },
          {
            foreignKeyName: "deals_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "deals_enriched"
            referencedColumns: ["category_id"]
          },
        ]
      }
      deals_deal_categories: {
        Row: {
          deal_category_id: string
          deal_id: string
        }
        Insert: {
          deal_category_id: string
          deal_id: string
        }
        Update: {
          deal_category_id?: string
          deal_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "deals_deal_categories_deal_category_id_fkey"
            columns: ["deal_category_id"]
            isOneToOne: false
            referencedRelation: "deal_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deals_deal_categories_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deals_deal_categories_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_dealid_view"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "deals_deal_categories_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_enriched"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "deals_deal_categories_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_with_info"
            referencedColumns: ["id"]
          },
        ]
      }
      document_embeddings: {
        Row: {
          content: string
          created_at: string
          embedding: string
          id: number
          metadata: Json
          version: number
        }
        Insert: {
          content: string
          created_at?: string
          embedding: string
          id?: number
          metadata: Json
          version?: number
        }
        Update: {
          content?: string
          created_at?: string
          embedding?: string
          id?: number
          metadata?: Json
          version?: number
        }
        Relationships: []
      }
      documents: {
        Row: {
          business_id: string
          created_at: string
          file_size: number | null
          file_type: string | null
          file_url: string
          id: string
          name: string
          updated_at: string
          user_id: string
        }
        Insert: {
          business_id: string
          created_at?: string
          file_size?: number | null
          file_type?: string | null
          file_url: string
          id?: string
          name: string
          updated_at?: string
          user_id: string
        }
        Update: {
          business_id?: string
          created_at?: string
          file_size?: number | null
          file_type?: string | null
          file_url?: string
          id?: string
          name?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "documents_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses_with_counts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "mv_business_availability"
            referencedColumns: ["business_id"]
          },
        ]
      }
      email_logs: {
        Row: {
          created_at: string
          error_message: string | null
          external_id: string | null
          id: string
          status: string
          subject: string
          template_id: string | null
          to_email: string
          variables: Json | null
        }
        Insert: {
          created_at?: string
          error_message?: string | null
          external_id?: string | null
          id?: string
          status: string
          subject: string
          template_id?: string | null
          to_email: string
          variables?: Json | null
        }
        Update: {
          created_at?: string
          error_message?: string | null
          external_id?: string | null
          id?: string
          status?: string
          subject?: string
          template_id?: string | null
          to_email?: string
          variables?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "email_logs_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "email_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      email_templates: {
        Row: {
          created_at: string
          description: string | null
          html_template: string
          id: string
          is_active: boolean | null
          name: string
          subject_template: string
          template_type: string
          updated_at: string
          variables: Json | null
        }
        Insert: {
          created_at?: string
          description?: string | null
          html_template: string
          id?: string
          is_active?: boolean | null
          name: string
          subject_template: string
          template_type: string
          updated_at?: string
          variables?: Json | null
        }
        Update: {
          created_at?: string
          description?: string | null
          html_template?: string
          id?: string
          is_active?: boolean | null
          name?: string
          subject_template?: string
          template_type?: string
          updated_at?: string
          variables?: Json | null
        }
        Relationships: []
      }
      github_integrations: {
        Row: {
          access_token: string
          authorized_at: string | null
          avatar_url: string | null
          business_id: string
          created_at: string | null
          expires_at: string | null
          github_user_id: number
          github_username: string
          id: string
          is_active: boolean | null
          refresh_token: string | null
          repository_access_type: string
          scope: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          access_token: string
          authorized_at?: string | null
          avatar_url?: string | null
          business_id: string
          created_at?: string | null
          expires_at?: string | null
          github_user_id: number
          github_username: string
          id?: string
          is_active?: boolean | null
          refresh_token?: string | null
          repository_access_type: string
          scope: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          access_token?: string
          authorized_at?: string | null
          avatar_url?: string | null
          business_id?: string
          created_at?: string | null
          expires_at?: string | null
          github_user_id?: number
          github_username?: string
          id?: string
          is_active?: boolean | null
          refresh_token?: string | null
          repository_access_type?: string
          scope?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "github_integrations_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "github_integrations_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses_with_counts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "github_integrations_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "mv_business_availability"
            referencedColumns: ["business_id"]
          },
          {
            foreignKeyName: "github_integrations_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      github_repositories: {
        Row: {
          clone_url: string
          created_at: string | null
          default_branch: string | null
          full_name: string
          github_repo_id: number
          id: string
          integration_id: string
          is_private: boolean | null
          name: string
          owner_login: string
          owner_type: string
          selected_at: string | null
          ssh_url: string
        }
        Insert: {
          clone_url: string
          created_at?: string | null
          default_branch?: string | null
          full_name: string
          github_repo_id: number
          id?: string
          integration_id: string
          is_private?: boolean | null
          name: string
          owner_login: string
          owner_type: string
          selected_at?: string | null
          ssh_url: string
        }
        Update: {
          clone_url?: string
          created_at?: string | null
          default_branch?: string | null
          full_name?: string
          github_repo_id?: number
          id?: string
          integration_id?: string
          is_private?: boolean | null
          name?: string
          owner_login?: string
          owner_type?: string
          selected_at?: string | null
          ssh_url?: string
        }
        Relationships: [
          {
            foreignKeyName: "github_repositories_integration_id_fkey"
            columns: ["integration_id"]
            isOneToOne: false
            referencedRelation: "github_integrations"
            referencedColumns: ["id"]
          },
        ]
      }
      global_settings: {
        Row: {
          created_at: string
          description: string | null
          id: string
          property: string
          type: Database["public"]["Enums"]["global_setting_type"]
          updated_at: string
          value: string | null
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          property: string
          type?: Database["public"]["Enums"]["global_setting_type"]
          updated_at?: string
          value?: string | null
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          property?: string
          type?: Database["public"]["Enums"]["global_setting_type"]
          updated_at?: string
          value?: string | null
        }
        Relationships: []
      }
      globalsetting: {
        Row: {
          created_at: string
          demo_location: Json | null
          fallback_location: Json | null
          id: number
          map_demo: boolean | null
        }
        Insert: {
          created_at?: string
          demo_location?: Json | null
          fallback_location?: Json | null
          id?: number
          map_demo?: boolean | null
        }
        Update: {
          created_at?: string
          demo_location?: Json | null
          fallback_location?: Json | null
          id?: number
          map_demo?: boolean | null
        }
        Relationships: []
      }
      group_booking_shares: {
        Row: {
          booking_id: string
          created_at: string
          group_id: string
          id: string
          message: string | null
          shared_by: string
        }
        Insert: {
          booking_id: string
          created_at?: string
          group_id: string
          id?: string
          message?: string | null
          shared_by: string
        }
        Update: {
          booking_id?: string
          created_at?: string
          group_id?: string
          id?: string
          message?: string | null
          shared_by?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_group_booking_shares_member"
            columns: ["group_id", "shared_by"]
            isOneToOne: false
            referencedRelation: "group_members"
            referencedColumns: ["group_id", "user_id"]
          },
          {
            foreignKeyName: "group_booking_shares_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "booking_with_all_details"
            referencedColumns: ["booking_id"]
          },
          {
            foreignKeyName: "group_booking_shares_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "bookings"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_booking_shares_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_booking_shares_shared_by_fkey"
            columns: ["shared_by"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      group_deal_shares: {
        Row: {
          created_at: string
          deal_id: string
          group_id: string
          id: string
          message: string | null
          shared_by: string
        }
        Insert: {
          created_at?: string
          deal_id: string
          group_id: string
          id?: string
          message?: string | null
          shared_by: string
        }
        Update: {
          created_at?: string
          deal_id?: string
          group_id?: string
          id?: string
          message?: string | null
          shared_by?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_group_deal_shares_member"
            columns: ["group_id", "shared_by"]
            isOneToOne: false
            referencedRelation: "group_members"
            referencedColumns: ["group_id", "user_id"]
          },
          {
            foreignKeyName: "group_deal_shares_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_deal_shares_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_dealid_view"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "group_deal_shares_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_enriched"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "group_deal_shares_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_with_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_deal_shares_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_deal_shares_shared_by_fkey"
            columns: ["shared_by"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      group_invites: {
        Row: {
          created_at: string | null
          group_id: string
          id: string
          invited_by: string
          invited_user_id: string
          status: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          group_id: string
          id?: string
          invited_by: string
          invited_user_id: string
          status?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          group_id?: string
          id?: string
          invited_by?: string
          invited_user_id?: string
          status?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "group_invites_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_invites_invited_by_fkey"
            columns: ["invited_by"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_invites_invited_user_id_fkey"
            columns: ["invited_user_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      group_members: {
        Row: {
          group_id: string
          id: string
          joined_at: string
          role: string
          user_id: string
        }
        Insert: {
          group_id: string
          id?: string
          joined_at?: string
          role?: string
          user_id: string
        }
        Update: {
          group_id?: string
          id?: string
          joined_at?: string
          role?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "group_members_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      groups: {
        Row: {
          avatar_url: string | null
          created_at: string
          created_by: string
          description: string | null
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          created_by: string
          description?: string | null
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          created_by?: string
          description?: string | null
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "groups_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      integration_authorizations: {
        Row: {
          access_token: string
          authorized_at: string
          created_at: string
          expires_at: string | null
          id: string
          integration_id: string
          is_active: boolean
          metadata: Json
          provider: string
          refresh_token: string | null
          scope: string | null
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          access_token: string
          authorized_at?: string
          created_at?: string
          expires_at?: string | null
          id?: string
          integration_id: string
          is_active?: boolean
          metadata?: Json
          provider: string
          refresh_token?: string | null
          scope?: string | null
          status?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          access_token?: string
          authorized_at?: string
          created_at?: string
          expires_at?: string | null
          id?: string
          integration_id?: string
          is_active?: boolean
          metadata?: Json
          provider?: string
          refresh_token?: string | null
          scope?: string | null
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "integration_authorizations_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      leads: {
        Row: {
          Address: string | null
          Categories: Json | null
          Category: string | null
          city: string | null
          country: string | null
          created_at: string
          discords: string | null
          Email: string | null
          facebooks: string | null
          google_maps_url: string | null
          google_place_id: string | null
          id: number
          image_url: string | null
          instagrams: string | null
          Latitude: number | null
          linkedIns: string | null
          loaded: boolean
          Longitude: number | null
          mapsUrl: string | null
          Name: string | null
          opening_hours_type: string | null
          Operating_Hours: string | null
          Phone: string | null
          pinterests: string | null
          Rating: number | null
          Review: string | null
          score_capacity_proxy: number | null
          score_digital_presence: number | null
          score_rating_quality: number | null
          source: string | null
          state: string | null
          tbl_id: string | null
          tiktoks: string | null
          total_score: number | null
          twitters: string | null
          user_ratings_total: number | null
          Website: string | null
          youtubes: string | null
          zip_code: string | null
        }
        Insert: {
          Address?: string | null
          Categories?: Json | null
          Category?: string | null
          city?: string | null
          country?: string | null
          created_at?: string
          discords?: string | null
          Email?: string | null
          facebooks?: string | null
          google_maps_url?: string | null
          google_place_id?: string | null
          id?: number
          image_url?: string | null
          instagrams?: string | null
          Latitude?: number | null
          linkedIns?: string | null
          loaded?: boolean
          Longitude?: number | null
          mapsUrl?: string | null
          Name?: string | null
          opening_hours_type?: string | null
          Operating_Hours?: string | null
          Phone?: string | null
          pinterests?: string | null
          Rating?: number | null
          Review?: string | null
          score_capacity_proxy?: number | null
          score_digital_presence?: number | null
          score_rating_quality?: number | null
          source?: string | null
          state?: string | null
          tbl_id?: string | null
          tiktoks?: string | null
          total_score?: number | null
          twitters?: string | null
          user_ratings_total?: number | null
          Website?: string | null
          youtubes?: string | null
          zip_code?: string | null
        }
        Update: {
          Address?: string | null
          Categories?: Json | null
          Category?: string | null
          city?: string | null
          country?: string | null
          created_at?: string
          discords?: string | null
          Email?: string | null
          facebooks?: string | null
          google_maps_url?: string | null
          google_place_id?: string | null
          id?: number
          image_url?: string | null
          instagrams?: string | null
          Latitude?: number | null
          linkedIns?: string | null
          loaded?: boolean
          Longitude?: number | null
          mapsUrl?: string | null
          Name?: string | null
          opening_hours_type?: string | null
          Operating_Hours?: string | null
          Phone?: string | null
          pinterests?: string | null
          Rating?: number | null
          Review?: string | null
          score_capacity_proxy?: number | null
          score_digital_presence?: number | null
          score_rating_quality?: number | null
          source?: string | null
          state?: string | null
          tbl_id?: string | null
          tiktoks?: string | null
          total_score?: number | null
          twitters?: string | null
          user_ratings_total?: number | null
          Website?: string | null
          youtubes?: string | null
          zip_code?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_leads_tbl_id"
            columns: ["tbl_id"]
            isOneToOne: false
            referencedRelation: "tableid"
            referencedColumns: ["tbl_id"]
          },
        ]
      }
      ln_canvases: {
        Row: {
          created_at: string
          created_by: string | null
          id: string
          name: string
          project_id: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          id?: string
          name?: string
          project_id?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string | null
          id?: string
          name?: string
          project_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "ln_canvases_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ln_canvases_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "ln_projects"
            referencedColumns: ["id"]
          },
        ]
      }
      ln_items: {
        Row: {
          content: string | null
          created_at: string
          custom_color: string | null
          details: string | null
          id: string
          order_num: number
          position_x: number | null
          position_y: number | null
          section_id: string | null
          updated_at: string
          z_index: number | null
        }
        Insert: {
          content?: string | null
          created_at?: string
          custom_color?: string | null
          details?: string | null
          id?: string
          order_num: number
          position_x?: number | null
          position_y?: number | null
          section_id?: string | null
          updated_at?: string
          z_index?: number | null
        }
        Update: {
          content?: string | null
          created_at?: string
          custom_color?: string | null
          details?: string | null
          id?: string
          order_num?: number
          position_x?: number | null
          position_y?: number | null
          section_id?: string | null
          updated_at?: string
          z_index?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "ln_items_section_id_fkey"
            columns: ["section_id"]
            isOneToOne: false
            referencedRelation: "ln_canvas_with_sections"
            referencedColumns: ["section_id"]
          },
          {
            foreignKeyName: "ln_items_section_id_fkey"
            columns: ["section_id"]
            isOneToOne: false
            referencedRelation: "ln_sections"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ln_items_section_id_fkey"
            columns: ["section_id"]
            isOneToOne: false
            referencedRelation: "ln_sections_with_items"
            referencedColumns: ["section_id"]
          },
        ]
      }
      ln_projects: {
        Row: {
          context: string | null
          created_at: string
          created_by: string | null
          description: string | null
          id: string
          mission: string | null
          name: string
          team_id: string
          updated_at: string
          values: Json | null
          vision: string | null
        }
        Insert: {
          context?: string | null
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          mission?: string | null
          name: string
          team_id: string
          updated_at?: string
          values?: Json | null
          vision?: string | null
        }
        Update: {
          context?: string | null
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          mission?: string | null
          name?: string
          team_id?: string
          updated_at?: string
          values?: Json | null
          vision?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "ln_projects_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ln_projects_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "ln_team_owners"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "ln_projects_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "ln_teams"
            referencedColumns: ["id"]
          },
        ]
      }
      ln_sections: {
        Row: {
          canvas_id: string | null
          color: string
          created_at: string
          description: string | null
          id: string
          order_num: number
          section_key: string
          title: string
          updated_at: string
        }
        Insert: {
          canvas_id?: string | null
          color: string
          created_at?: string
          description?: string | null
          id?: string
          order_num: number
          section_key: string
          title: string
          updated_at?: string
        }
        Update: {
          canvas_id?: string | null
          color?: string
          created_at?: string
          description?: string | null
          id?: string
          order_num?: number
          section_key?: string
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "ln_sections_canvas_id_fkey"
            columns: ["canvas_id"]
            isOneToOne: false
            referencedRelation: "ln_canvas_with_sections"
            referencedColumns: ["canvas_id"]
          },
          {
            foreignKeyName: "ln_sections_canvas_id_fkey"
            columns: ["canvas_id"]
            isOneToOne: false
            referencedRelation: "ln_canvases"
            referencedColumns: ["id"]
          },
        ]
      }
      ln_team_members: {
        Row: {
          created_at: string
          id: string
          role: string
          team_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          role?: string
          team_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          role?: string
          team_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "ln_team_members_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "ln_team_owners"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "ln_team_members_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "ln_teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ln_team_members_user_id_fkey1"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "booking_with_all_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "ln_team_members_user_id_fkey1"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "messages_with_user_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "ln_team_members_user_id_fkey1"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_details"
            referencedColumns: ["id"]
          },
        ]
      }
      ln_teams: {
        Row: {
          created_at: string
          created_by: string
          description: string | null
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by: string
          description?: string | null
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string
          description?: string | null
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "ln_teams_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ln_teams_created_by_fkey1"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "booking_with_all_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "ln_teams_created_by_fkey1"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "messages_with_user_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "ln_teams_created_by_fkey1"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_details"
            referencedColumns: ["id"]
          },
        ]
      }
      messages: {
        Row: {
          content: string
          conversation_id: string | null
          created_at: string
          id: string
          metadata: Json
          read_at: string | null
          user_id: string
        }
        Insert: {
          content: string
          conversation_id?: string | null
          created_at?: string
          id?: string
          metadata: Json
          read_at?: string | null
          user_id: string
        }
        Update: {
          content?: string
          conversation_id?: string | null
          created_at?: string
          id?: string
          metadata?: Json
          read_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations_with_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "booking_with_all_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "messages_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "messages_with_user_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "messages_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_details"
            referencedColumns: ["id"]
          },
        ]
      }
      multi_service_plans: {
        Row: {
          created_at: string
          id: string
          optimized_plan: Json | null
          request_text: string
          services: Json
          status: string
          total_budget: number | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          optimized_plan?: Json | null
          request_text: string
          services: Json
          status?: string
          total_budget?: number | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          optimized_plan?: Json | null
          request_text?: string
          services?: Json
          status?: string
          total_budget?: number | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      notifications: {
        Row: {
          created_at: string
          entity: string
          entity_id: string
          id: string
          read_at: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          entity: string
          entity_id: string
          id?: string
          read_at?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          entity?: string
          entity_id?: string
          id?: string
          read_at?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      pm_activities: {
        Row: {
          action: string
          created_at: string
          id: string
          metadata: Json | null
          target_id: string
          target_name: string
          target_type: string
          user_id: string
        }
        Insert: {
          action: string
          created_at?: string
          id?: string
          metadata?: Json | null
          target_id: string
          target_name: string
          target_type: string
          user_id: string
        }
        Update: {
          action?: string
          created_at?: string
          id?: string
          metadata?: Json | null
          target_id?: string
          target_name?: string
          target_type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pm_activities_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      pm_milestones: {
        Row: {
          color: string | null
          created_at: string
          description: string | null
          due_date: string | null
          id: string
          project_id: string
          start_date: string | null
          status: string
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          color?: string | null
          created_at?: string
          description?: string | null
          due_date?: string | null
          id?: string
          project_id: string
          start_date?: string | null
          status?: string
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          color?: string | null
          created_at?: string
          description?: string | null
          due_date?: string | null
          id?: string
          project_id?: string
          start_date?: string | null
          status?: string
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pm_milestones_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "pm_combined_task_project_stats_view"
            referencedColumns: ["project_id"]
          },
          {
            foreignKeyName: "pm_milestones_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "pm_project_task_stats_view"
            referencedColumns: ["project_id"]
          },
          {
            foreignKeyName: "pm_milestones_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "pm_projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pm_milestones_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      pm_projects: {
        Row: {
          color: string | null
          created_at: string
          description: string | null
          due_date: string | null
          id: string
          start_date: string | null
          status: string
          team_id: string | null
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          color?: string | null
          created_at?: string
          description?: string | null
          due_date?: string | null
          id?: string
          start_date?: string | null
          status?: string
          team_id?: string | null
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          color?: string | null
          created_at?: string
          description?: string | null
          due_date?: string | null
          id?: string
          start_date?: string | null
          status?: string
          team_id?: string | null
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pm_projects_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "pm_teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pm_projects_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      pm_tasks: {
        Row: {
          category: string | null
          completed: boolean
          created_at: string
          description: string | null
          due_date: string | null
          id: string
          milestone_id: string | null
          priority: Database["public"]["Enums"]["task_priority_enum"]
          status: Database["public"]["Enums"]["task_status_enum"] | null
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          category?: string | null
          completed?: boolean
          created_at?: string
          description?: string | null
          due_date?: string | null
          id?: string
          milestone_id?: string | null
          priority?: Database["public"]["Enums"]["task_priority_enum"]
          status?: Database["public"]["Enums"]["task_status_enum"] | null
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          category?: string | null
          completed?: boolean
          created_at?: string
          description?: string | null
          due_date?: string | null
          id?: string
          milestone_id?: string | null
          priority?: Database["public"]["Enums"]["task_priority_enum"]
          status?: Database["public"]["Enums"]["task_status_enum"] | null
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pm_tasks_milestone_id_fkey"
            columns: ["milestone_id"]
            isOneToOne: false
            referencedRelation: "pm_combined_task_project_stats_view"
            referencedColumns: ["milestone_id"]
          },
          {
            foreignKeyName: "pm_tasks_milestone_id_fkey"
            columns: ["milestone_id"]
            isOneToOne: false
            referencedRelation: "pm_milestones"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pm_tasks_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      pm_team_members: {
        Row: {
          created_at: string
          id: string
          role: string
          team_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          role?: string
          team_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          role?: string
          team_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pm_team_members_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "pm_teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pm_team_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "booking_with_all_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "pm_team_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "messages_with_user_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "pm_team_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_details"
            referencedColumns: ["id"]
          },
        ]
      }
      pm_teams: {
        Row: {
          created_at: string
          created_by: string
          description: string | null
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by: string
          description?: string | null
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string
          description?: string | null
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      pricing_tiers: {
        Row: {
          allowed_agents: string[] | null
          created_at: string
          description: string | null
          discount_expire_date: string | null
          discounted_price: number | null
          discounted_price_yearly: number | null
          enable_discount: boolean
          features: Json
          id: string
          is_active: boolean
          max_agents: number
          max_businesses: number
          name: string
          price_monthly: number
          price_yearly: number
          tier_type: Database["public"]["Enums"]["pricing_tier_type"]
          updated_at: string
        }
        Insert: {
          allowed_agents?: string[] | null
          created_at?: string
          description?: string | null
          discount_expire_date?: string | null
          discounted_price?: number | null
          discounted_price_yearly?: number | null
          enable_discount?: boolean
          features?: Json
          id?: string
          is_active?: boolean
          max_agents?: number
          max_businesses?: number
          name: string
          price_monthly: number
          price_yearly: number
          tier_type: Database["public"]["Enums"]["pricing_tier_type"]
          updated_at?: string
        }
        Update: {
          allowed_agents?: string[] | null
          created_at?: string
          description?: string | null
          discount_expire_date?: string | null
          discounted_price?: number | null
          discounted_price_yearly?: number | null
          enable_discount?: boolean
          features?: Json
          id?: string
          is_active?: boolean
          max_agents?: number
          max_businesses?: number
          name?: string
          price_monthly?: number
          price_yearly?: number
          tier_type?: Database["public"]["Enums"]["pricing_tier_type"]
          updated_at?: string
        }
        Relationships: []
      }
      reminders: {
        Row: {
          advance_minutes: number | null
          attempts: number | null
          created_at: string
          description: string | null
          entity_id: string | null
          entity_type: string | null
          id: string
          max_attempts: number | null
          metadata: Json | null
          notification_method: Database["public"]["Enums"]["notification_method_enum"]
          reminder_time: string
          repeat_interval: string | null
          status: Database["public"]["Enums"]["reminder_status_enum"]
          title: string
          type: Database["public"]["Enums"]["reminder_type_enum"]
          updated_at: string
          user_id: string
        }
        Insert: {
          advance_minutes?: number | null
          attempts?: number | null
          created_at?: string
          description?: string | null
          entity_id?: string | null
          entity_type?: string | null
          id?: string
          max_attempts?: number | null
          metadata?: Json | null
          notification_method?: Database["public"]["Enums"]["notification_method_enum"]
          reminder_time: string
          repeat_interval?: string | null
          status?: Database["public"]["Enums"]["reminder_status_enum"]
          title: string
          type?: Database["public"]["Enums"]["reminder_type_enum"]
          updated_at?: string
          user_id: string
        }
        Update: {
          advance_minutes?: number | null
          attempts?: number | null
          created_at?: string
          description?: string | null
          entity_id?: string | null
          entity_type?: string | null
          id?: string
          max_attempts?: number | null
          metadata?: Json | null
          notification_method?: Database["public"]["Enums"]["notification_method_enum"]
          reminder_time?: string
          repeat_interval?: string | null
          status?: Database["public"]["Enums"]["reminder_status_enum"]
          title?: string
          type?: Database["public"]["Enums"]["reminder_type_enum"]
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      reviews: {
        Row: {
          booking_id: string
          business_id: string
          comment: string | null
          created_at: string
          id: string
          rating: number
          sentiment: Database["public"]["Enums"]["review_sentiment"] | null
          updated_at: string
          user_id: string
        }
        Insert: {
          booking_id: string
          business_id: string
          comment?: string | null
          created_at?: string
          id?: string
          rating: number
          sentiment?: Database["public"]["Enums"]["review_sentiment"] | null
          updated_at?: string
          user_id: string
        }
        Update: {
          booking_id?: string
          business_id?: string
          comment?: string | null
          created_at?: string
          id?: string
          rating?: number
          sentiment?: Database["public"]["Enums"]["review_sentiment"] | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "reviews_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "booking_with_all_details"
            referencedColumns: ["booking_id"]
          },
          {
            foreignKeyName: "reviews_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "bookings"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reviews_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reviews_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses_with_counts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reviews_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "mv_business_availability"
            referencedColumns: ["business_id"]
          },
          {
            foreignKeyName: "reviews_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      spatial_ref_sys: {
        Row: {
          auth_name: string | null
          auth_srid: number | null
          proj4text: string | null
          srid: number
          srtext: string | null
        }
        Insert: {
          auth_name?: string | null
          auth_srid?: number | null
          proj4text?: string | null
          srid: number
          srtext?: string | null
        }
        Update: {
          auth_name?: string | null
          auth_srid?: number | null
          proj4text?: string | null
          srid?: number
          srtext?: string | null
        }
        Relationships: []
      }
      tableid: {
        Row: {
          created_at: string
          tbl_id: string
          tbl_name: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          tbl_id?: string
          tbl_name: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          tbl_id?: string
          tbl_name?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tableid_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      time_slot_bookings: {
        Row: {
          booked_seats: number
          booking_date: string | null
          booking_id: string
          created_at: string
          day_of_week: number
          deal_id: string
          end_time: string
          fake: boolean
          id: string
          start_time: string
        }
        Insert: {
          booked_seats?: number
          booking_date?: string | null
          booking_id: string
          created_at?: string
          day_of_week: number
          deal_id: string
          end_time: string
          fake?: boolean
          id?: string
          start_time: string
        }
        Update: {
          booked_seats?: number
          booking_date?: string | null
          booking_id?: string
          created_at?: string
          day_of_week?: number
          deal_id?: string
          end_time?: string
          fake?: boolean
          id?: string
          start_time?: string
        }
        Relationships: [
          {
            foreignKeyName: "time_slot_bookings_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "booking_with_all_details"
            referencedColumns: ["booking_id"]
          },
          {
            foreignKeyName: "time_slot_bookings_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "bookings"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_slot_bookings_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_slot_bookings_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_dealid_view"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "time_slot_bookings_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_enriched"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "time_slot_bookings_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_with_info"
            referencedColumns: ["id"]
          },
        ]
      }
      user_campaigns: {
        Row: {
          campaign_id: string
          created_at: string
          id: string
          sent_at: string | null
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          campaign_id: string
          created_at?: string
          id?: string
          sent_at?: string | null
          status?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          campaign_id?: string
          created_at?: string
          id?: string
          sent_at?: string | null
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_campaigns_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
        ]
      }
      user_deal_interactions: {
        Row: {
          count: number | null
          created_at: string
          deal_id: string
          id: string
          interaction_type: string
          updated_at: string
          user_id: string
        }
        Insert: {
          count?: number | null
          created_at?: string
          deal_id: string
          id?: string
          interaction_type: string
          updated_at?: string
          user_id: string
        }
        Update: {
          count?: number | null
          created_at?: string
          deal_id?: string
          id?: string
          interaction_type?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_deal_interactions_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_deal_interactions_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_dealid_view"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "user_deal_interactions_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_enriched"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "user_deal_interactions_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_with_info"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_deal_interactions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "booking_with_all_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "user_deal_interactions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "messages_with_user_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "user_deal_interactions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_details"
            referencedColumns: ["id"]
          },
        ]
      }
      user_details: {
        Row: {
          avatar_url: string | null
          business_mode: boolean
          created_at: string
          default_business_id: string | null
          demo_location: Json | null
          display_name: string | null
          email: string | null
          fake: boolean
          favorite_deals: Json | null
          first_name: string | null
          id: string
          last_name: string | null
          location_enabled: boolean
          map_demo: boolean | null
          onboarding_completed: boolean
          phone_number: string | null
          recently_viewed_deals: Json | null
          selected_assistant_id: string | null
          thread_id: string | null
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          business_mode?: boolean
          created_at?: string
          default_business_id?: string | null
          demo_location?: Json | null
          display_name?: string | null
          email?: string | null
          fake?: boolean
          favorite_deals?: Json | null
          first_name?: string | null
          id: string
          last_name?: string | null
          location_enabled?: boolean
          map_demo?: boolean | null
          onboarding_completed?: boolean
          phone_number?: string | null
          recently_viewed_deals?: Json | null
          selected_assistant_id?: string | null
          thread_id?: string | null
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          business_mode?: boolean
          created_at?: string
          default_business_id?: string | null
          demo_location?: Json | null
          display_name?: string | null
          email?: string | null
          fake?: boolean
          favorite_deals?: Json | null
          first_name?: string | null
          id?: string
          last_name?: string | null
          location_enabled?: boolean
          map_demo?: boolean | null
          onboarding_completed?: boolean
          phone_number?: string | null
          recently_viewed_deals?: Json | null
          selected_assistant_id?: string | null
          thread_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_details_default_business_id_fkey"
            columns: ["default_business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_details_default_business_id_fkey"
            columns: ["default_business_id"]
            isOneToOne: false
            referencedRelation: "businesses_with_counts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_details_default_business_id_fkey"
            columns: ["default_business_id"]
            isOneToOne: false
            referencedRelation: "mv_business_availability"
            referencedColumns: ["business_id"]
          },
          {
            foreignKeyName: "user_details_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_details_selected_assistant_id_fkey"
            columns: ["selected_assistant_id"]
            isOneToOne: false
            referencedRelation: "ai_assistant_profile"
            referencedColumns: ["id"]
          },
        ]
      }
      user_location_history: {
        Row: {
          created_at: string
          id: string
          latitude: number
          longitude: number
          recorded_at: string
          source: string | null
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          latitude: number
          longitude: number
          recorded_at?: string
          source?: string | null
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          latitude?: number
          longitude?: number
          recorded_at?: string
          source?: string | null
          user_id?: string
        }
        Relationships: []
      }
      user_preferences: {
        Row: {
          categories: string[] | null
          created_at: string | null
          id: string
          notification_preferences: Json | null
          onboarding_completed: boolean | null
          price_range: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          categories?: string[] | null
          created_at?: string | null
          id?: string
          notification_preferences?: Json | null
          onboarding_completed?: boolean | null
          price_range?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          categories?: string[] | null
          created_at?: string | null
          id?: string
          notification_preferences?: Json | null
          onboarding_completed?: boolean | null
          price_range?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_preferences_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      booking_with_all_details: {
        Row: {
          booking_date: string | null
          booking_id: string | null
          booking_time: string | null
          business_address: string | null
          business_name: string | null
          deal_description: string | null
          deal_title: string | null
          email: string | null
          fake: boolean | null
          first_name: string | null
          last_name: string | null
          status: string | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_details_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      businesses_with_counts: {
        Row: {
          address: string | null
          booking_count: number | null
          category_id: string | null
          city: string | null
          country: string | null
          created_at: string | null
          deal_count: number | null
          deal_count_draft: number | null
          deal_count_expired: number | null
          deal_count_published: number | null
          description: string | null
          email: string | null
          fake: boolean | null
          id: string | null
          is_owner: boolean | null
          latitude: number | null
          longitude: number | null
          name: string | null
          owner_id: string | null
          pending_booking_count: number | null
          phone: string | null
          photos: string[] | null
          state: string | null
          updated_at: string | null
          website: string | null
          zip_code: string | null
        }
        Insert: {
          address?: string | null
          booking_count?: never
          category_id?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          deal_count?: never
          deal_count_draft?: never
          deal_count_expired?: never
          deal_count_published?: never
          description?: string | null
          email?: string | null
          fake?: boolean | null
          id?: string | null
          is_owner?: never
          latitude?: number | null
          longitude?: number | null
          name?: string | null
          owner_id?: string | null
          pending_booking_count?: never
          phone?: string | null
          photos?: string[] | null
          state?: string | null
          updated_at?: string | null
          website?: string | null
          zip_code?: string | null
        }
        Update: {
          address?: string | null
          booking_count?: never
          category_id?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          deal_count?: never
          deal_count_draft?: never
          deal_count_expired?: never
          deal_count_published?: never
          description?: string | null
          email?: string | null
          fake?: boolean | null
          id?: string | null
          is_owner?: never
          latitude?: number | null
          longitude?: number | null
          name?: string | null
          owner_id?: string | null
          pending_booking_count?: never
          phone?: string | null
          photos?: string[] | null
          state?: string | null
          updated_at?: string | null
          website?: string | null
          zip_code?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "businesses_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "businesses_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "deals_dealid_view"
            referencedColumns: ["category_id"]
          },
          {
            foreignKeyName: "businesses_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "deals_enriched"
            referencedColumns: ["category_id"]
          },
          {
            foreignKeyName: "businesses_owner_id_fkey"
            columns: ["owner_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      conversations_with_details: {
        Row: {
          booking_id: string | null
          business_id: string | null
          business_name: string | null
          business_photo: string | null
          created_at: string | null
          deal_id: string | null
          deal_image: string | null
          deal_title: string | null
          id: string | null
          last_message: Json | null
          last_message_at: string | null
          owner_id: string | null
          participants: Json | null
          type: Database["public"]["Enums"]["conversation_type"] | null
          unread_count: number | null
          updated_at: string | null
        }
        Relationships: [
          {
            foreignKeyName: "businesses_owner_id_fkey"
            columns: ["owner_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "conversations_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "booking_with_all_details"
            referencedColumns: ["booking_id"]
          },
          {
            foreignKeyName: "conversations_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "bookings"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "conversations_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "conversations_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses_with_counts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "conversations_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "mv_business_availability"
            referencedColumns: ["business_id"]
          },
          {
            foreignKeyName: "conversations_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "conversations_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_dealid_view"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "conversations_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_enriched"
            referencedColumns: ["dealid"]
          },
          {
            foreignKeyName: "conversations_deal_id_fkey"
            columns: ["deal_id"]
            isOneToOne: false
            referencedRelation: "deals_with_info"
            referencedColumns: ["id"]
          },
        ]
      }
      deals_dealid_view: {
        Row: {
          auto_confirm: boolean | null
          business_id: string | null
          category_id: string | null
          category_name: string | null
          created_at: string | null
          dealid: string | null
          description: string | null
          discount_percentage: number | null
          discounted_price: number | null
          end_date: string | null
          fake: boolean | null
          images: string[] | null
          original_price: number | null
          owner_id: string | null
          start_date: string | null
          status: Database["public"]["Enums"]["deal_status_enum"] | null
          terms_conditions: string | null
          time_slots: Json | null
          title: string | null
          updated_at: string | null
        }
        Relationships: [
          {
            foreignKeyName: "businesses_owner_id_fkey"
            columns: ["owner_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deals_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deals_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses_with_counts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deals_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "mv_business_availability"
            referencedColumns: ["business_id"]
          },
        ]
      }
      deals_enriched: {
        Row: {
          auto_confirm: boolean | null
          busineses_score: number | null
          business_address: string | null
          business_city: string | null
          business_country: string | null
          business_id: string | null
          business_name: string | null
          business_owner_id: string | null
          business_position_latitude: number | null
          business_position_longitude: number | null
          business_state: string | null
          business_zip_code: string | null
          category_id: string | null
          category_name: string | null
          created_at: string | null
          deal_categories: Json | null
          deal_category_names: string[] | null
          dealid: string | null
          description: string | null
          discount_percentage: number | null
          discounted_price: number | null
          end_date: string | null
          fake: boolean | null
          images: string[] | null
          original_price: number | null
          start_date: string | null
          status: Database["public"]["Enums"]["deal_status_enum"] | null
          terms_conditions: string | null
          time_slots: Json | null
          title: string | null
          updated_at: string | null
        }
        Relationships: [
          {
            foreignKeyName: "businesses_owner_id_fkey"
            columns: ["business_owner_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deals_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deals_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses_with_counts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deals_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "mv_business_availability"
            referencedColumns: ["business_id"]
          },
        ]
      }
      deals_with_info: {
        Row: {
          auto_confirm: boolean | null
          busineses_score: number | null
          business_address: string | null
          business_city: string | null
          business_country: string | null
          business_id: string | null
          business_name: string | null
          business_owner_id: string | null
          business_state: string | null
          business_zip_code: string | null
          category_name: string | null
          description: string | null
          discount_percentage: number | null
          discounted_price: number | null
          end_date: string | null
          fake: boolean | null
          id: string | null
          original_price: number | null
          start_date: string | null
          status: Database["public"]["Enums"]["deal_status_enum"] | null
          time_slots: Json | null
          title: string | null
        }
        Relationships: [
          {
            foreignKeyName: "businesses_owner_id_fkey"
            columns: ["business_owner_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deals_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deals_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "businesses_with_counts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deals_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "mv_business_availability"
            referencedColumns: ["business_id"]
          },
        ]
      }
      geography_columns: {
        Row: {
          coord_dimension: number | null
          f_geography_column: unknown | null
          f_table_catalog: unknown | null
          f_table_name: unknown | null
          f_table_schema: unknown | null
          srid: number | null
          type: string | null
        }
        Relationships: []
      }
      geometry_columns: {
        Row: {
          coord_dimension: number | null
          f_geometry_column: unknown | null
          f_table_catalog: string | null
          f_table_name: unknown | null
          f_table_schema: unknown | null
          srid: number | null
          type: string | null
        }
        Insert: {
          coord_dimension?: number | null
          f_geometry_column?: unknown | null
          f_table_catalog?: string | null
          f_table_name?: unknown | null
          f_table_schema?: unknown | null
          srid?: number | null
          type?: string | null
        }
        Update: {
          coord_dimension?: number | null
          f_geometry_column?: unknown | null
          f_table_catalog?: string | null
          f_table_name?: unknown | null
          f_table_schema?: unknown | null
          srid?: number | null
          type?: string | null
        }
        Relationships: []
      }
      ln_canvas_with_sections: {
        Row: {
          canvas_created_at: string | null
          canvas_id: string | null
          canvas_id_fk: string | null
          canvas_name: string | null
          canvas_updated_at: string | null
          project_id: string | null
          section_created_at: string | null
          section_description: string | null
          section_id: string | null
          section_order: number | null
          section_title: string | null
          section_type: string | null
          section_updated_at: string | null
        }
        Relationships: [
          {
            foreignKeyName: "ln_canvases_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "ln_projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ln_sections_canvas_id_fkey"
            columns: ["canvas_id_fk"]
            isOneToOne: false
            referencedRelation: "ln_canvas_with_sections"
            referencedColumns: ["canvas_id"]
          },
          {
            foreignKeyName: "ln_sections_canvas_id_fkey"
            columns: ["canvas_id_fk"]
            isOneToOne: false
            referencedRelation: "ln_canvases"
            referencedColumns: ["id"]
          },
        ]
      }
      ln_sections_with_items: {
        Row: {
          canvas_id: string | null
          item_content: string | null
          item_created_at: string | null
          item_id: string | null
          item_order: number | null
          item_updated_at: string | null
          section_description: string | null
          section_id: string | null
          section_key: string | null
          section_order: number | null
          section_title: string | null
        }
        Relationships: [
          {
            foreignKeyName: "ln_sections_canvas_id_fkey"
            columns: ["canvas_id"]
            isOneToOne: false
            referencedRelation: "ln_canvas_with_sections"
            referencedColumns: ["canvas_id"]
          },
          {
            foreignKeyName: "ln_sections_canvas_id_fkey"
            columns: ["canvas_id"]
            isOneToOne: false
            referencedRelation: "ln_canvases"
            referencedColumns: ["id"]
          },
        ]
      }
      ln_team_members_info: {
        Row: {
          created_at: string | null
          email: string | null
          full_name: string | null
          id: string | null
          name: string | null
          role: string | null
          team_id: string | null
          updated_at: string | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "ln_team_members_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "ln_team_owners"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "ln_team_members_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "ln_teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ln_team_members_user_id_fkey1"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "booking_with_all_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "ln_team_members_user_id_fkey1"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "messages_with_user_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "ln_team_members_user_id_fkey1"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_details"
            referencedColumns: ["id"]
          },
        ]
      }
      ln_team_owners: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          email: string | null
          first_name: string | null
          last_name: string | null
          team_description: string | null
          team_id: string | null
          team_name: string | null
          updated_at: string | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "ln_team_members_user_id_fkey1"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "booking_with_all_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "ln_team_members_user_id_fkey1"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "messages_with_user_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "ln_team_members_user_id_fkey1"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_details"
            referencedColumns: ["id"]
          },
        ]
      }
      message_with_role: {
        Row: {
          business_name: string | null
          content: string | null
          conversation_id: string | null
          created_at: string | null
          first_name: string | null
          id: string | null
          metadata: Json | null
          read_at: string | null
          role: Database["public"]["Enums"]["conversation_role"] | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations_with_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "booking_with_all_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "messages_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "messages_with_user_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "messages_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_details"
            referencedColumns: ["id"]
          },
        ]
      }
      messages_with_user_details: {
        Row: {
          avatar_url: string | null
          content: string | null
          created_at: string | null
          first_name: string | null
          last_name: string | null
          message_id: string | null
          metadata: Json | null
          sender: string | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "messages_user_id_fkey"
            columns: ["sender"]
            isOneToOne: false
            referencedRelation: "booking_with_all_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "messages_user_id_fkey"
            columns: ["sender"]
            isOneToOne: false
            referencedRelation: "messages_with_user_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "messages_user_id_fkey"
            columns: ["sender"]
            isOneToOne: false
            referencedRelation: "user_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_details_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      mv_business_availability: {
        Row: {
          active_deals_count: number | null
          address: string | null
          booking_count: number | null
          business_id: string | null
          cancelled_booking_count: number | null
          category_id: string | null
          city: string | null
          completed_booking_count: number | null
          confirmed_booking_count: number | null
          country: string | null
          deal_count: number | null
          end_date: string | null
          expired_deals_count: number | null
          fake: boolean | null
          geom: unknown | null
          latitude: number | null
          longitude: number | null
          name: string | null
          owner_id: string | null
          pending_booking_count: number | null
          photos: string[] | null
          recent_bookings: number | null
          recent_bookings_previous: number | null
          start_date: string | null
          state: string | null
          time_slots: Json | null
          unique_clients_count: number | null
          zip_code: string | null
        }
        Relationships: [
          {
            foreignKeyName: "businesses_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "businesses_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "deals_dealid_view"
            referencedColumns: ["category_id"]
          },
          {
            foreignKeyName: "businesses_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "deals_enriched"
            referencedColumns: ["category_id"]
          },
          {
            foreignKeyName: "businesses_owner_id_fkey"
            columns: ["owner_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      pm_activities_with_details: {
        Row: {
          action: string | null
          avatar_url: string | null
          created_at: string | null
          display_name: string | null
          first_name: string | null
          id: string | null
          last_name: string | null
          metadata: Json | null
          target_id: string | null
          target_name: string | null
          target_type: string | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pm_activities_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_with_details"
            referencedColumns: ["id"]
          },
        ]
      }
      pm_combined_task_project_stats_view: {
        Row: {
          completed_tasks: number | null
          due_today_tasks: number | null
          in_progress_tasks: number | null
          milestone_id: string | null
          overdue_tasks: number | null
          project_id: string | null
          review_tasks: number | null
          todo_tasks: number | null
          total_tasks: number | null
        }
        Relationships: []
      }
      pm_project_task_stats_view: {
        Row: {
          completed_tasks: number | null
          due_today_tasks: number | null
          in_progress_tasks: number | null
          overdue_tasks: number | null
          project_id: string | null
          review_tasks: number | null
          todo_tasks: number | null
          total_tasks: number | null
        }
        Relationships: []
      }
      pm_task_stats_milestone_view: {
        Row: {
          completed_tasks: number | null
          due_today_tasks: number | null
          in_progress_tasks: number | null
          milestone_id: string | null
          overdue_tasks: number | null
          review_tasks: number | null
          todo_tasks: number | null
          total_tasks: number | null
        }
        Relationships: [
          {
            foreignKeyName: "pm_tasks_milestone_id_fkey"
            columns: ["milestone_id"]
            isOneToOne: false
            referencedRelation: "pm_combined_task_project_stats_view"
            referencedColumns: ["milestone_id"]
          },
          {
            foreignKeyName: "pm_tasks_milestone_id_fkey"
            columns: ["milestone_id"]
            isOneToOne: false
            referencedRelation: "pm_milestones"
            referencedColumns: ["id"]
          },
        ]
      }
      pm_team_members_info: {
        Row: {
          created_at: string | null
          email: string | null
          first_name: string | null
          id: string | null
          last_name: string | null
          name: string | null
          role: string | null
          team_id: string | null
          updated_at: string | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pm_team_members_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "pm_teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pm_team_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "booking_with_all_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "pm_team_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "messages_with_user_details"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "pm_team_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_details"
            referencedColumns: ["id"]
          },
        ]
      }
      types_view: {
        Row: {
          type_definition: string | null
        }
        Relationships: []
      }
      users_with_details: {
        Row: {
          avatar_url: string | null
          business_mode: boolean | null
          default_business_id: string | null
          display_name: string | null
          email: string | null
          first_name: string | null
          id: string | null
          last_name: string | null
          phone_number: string | null
          registered_at: string | null
          role: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_details_default_business_id_fkey"
            columns: ["default_business_id"]
            isOneToOne: false
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_details_default_business_id_fkey"
            columns: ["default_business_id"]
            isOneToOne: false
            referencedRelation: "businesses_with_counts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_details_default_business_id_fkey"
            columns: ["default_business_id"]
            isOneToOne: false
            referencedRelation: "mv_business_availability"
            referencedColumns: ["business_id"]
          },
        ]
      }
    }
    Functions: {
      _postgis_deprecate: {
        Args: { newname: string; oldname: string; version: string }
        Returns: undefined
      }
      _postgis_index_extent: {
        Args: { col: string; tbl: unknown }
        Returns: unknown
      }
      _postgis_pgsql_version: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      _postgis_scripts_pgsql_version: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      _postgis_selectivity: {
        Args: { att_name: string; geom: unknown; mode?: string; tbl: unknown }
        Returns: number
      }
      _st_3dintersects: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      _st_bestsrid: {
        Args: { "": unknown }
        Returns: number
      }
      _st_contains: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      _st_containsproperly: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      _st_coveredby: {
        Args:
          | { geog1: unknown; geog2: unknown }
          | { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      _st_covers: {
        Args:
          | { geog1: unknown; geog2: unknown }
          | { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      _st_crosses: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      _st_dwithin: {
        Args: {
          geog1: unknown
          geog2: unknown
          tolerance: number
          use_spheroid?: boolean
        }
        Returns: boolean
      }
      _st_equals: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      _st_intersects: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      _st_linecrossingdirection: {
        Args: { line1: unknown; line2: unknown }
        Returns: number
      }
      _st_longestline: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: unknown
      }
      _st_maxdistance: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: number
      }
      _st_orderingequals: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      _st_overlaps: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      _st_pointoutside: {
        Args: { "": unknown }
        Returns: unknown
      }
      _st_sortablehash: {
        Args: { geom: unknown }
        Returns: number
      }
      _st_touches: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      _st_voronoi: {
        Args: {
          clip?: unknown
          g1: unknown
          return_polygons?: boolean
          tolerance?: number
        }
        Returns: unknown
      }
      _st_within: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      addauth: {
        Args: { "": string }
        Returns: boolean
      }
      addgeometrycolumn: {
        Args:
          | {
              catalog_name: string
              column_name: string
              new_dim: number
              new_srid_in: number
              new_type: string
              schema_name: string
              table_name: string
              use_typmod?: boolean
            }
          | {
              column_name: string
              new_dim: number
              new_srid: number
              new_type: string
              schema_name: string
              table_name: string
              use_typmod?: boolean
            }
          | {
              column_name: string
              new_dim: number
              new_srid: number
              new_type: string
              table_name: string
              use_typmod?: boolean
            }
        Returns: string
      }
      binary_quantize: {
        Args: { "": string } | { "": unknown }
        Returns: unknown
      }
      box: {
        Args: { "": unknown } | { "": unknown }
        Returns: unknown
      }
      box2d: {
        Args: { "": unknown } | { "": unknown }
        Returns: unknown
      }
      box2d_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      box2d_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      box2df_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      box2df_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      box3d: {
        Args: { "": unknown } | { "": unknown }
        Returns: unknown
      }
      box3d_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      box3d_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      box3dtobox: {
        Args: { "": unknown }
        Returns: unknown
      }
      bytea: {
        Args: { "": unknown } | { "": unknown }
        Returns: string
      }
      deals_available: {
        Args: {
          p_at_date?: string
          p_at_time?: string
          p_date_from?: string
          p_date_to?: string
          p_deal_id?: string
          p_min_seats?: number
          p_time_from?: string
          p_time_to?: string
        }
        Returns: {
          available_seats: number
          deal_id: string
          deal_title: string
          end_time: string
          slot_date: string
          start_time: string
        }[]
      }
      deals_enriched_available: {
        Args:
          | {
              p_at_date?: string
              p_at_time?: string
              p_business_category_ids?: string[]
              p_business_category_names?: string[]
              p_city?: string
              p_date_from?: string
              p_date_to?: string
              p_deal_category_ids?: string[]
              p_deal_category_names?: string[]
              p_deal_id?: string
              p_lat?: number
              p_lon?: number
              p_min_seats?: number
              p_nearby?: boolean
              p_owner_id?: string
              p_price_max?: number
              p_price_min?: number
              p_radius_km?: number
              p_status?: string
              p_time_from?: string
              p_time_to?: string
            }
          | {
              p_at_date?: string
              p_at_time?: string
              p_business_category_ids?: string[]
              p_business_category_names?: string[]
              p_city?: string
              p_date_from?: string
              p_date_to?: string
              p_deal_category_ids?: string[]
              p_deal_category_names?: string[]
              p_deal_id?: string
              p_min_seats?: number
              p_owner_id?: string
              p_price_max?: number
              p_price_min?: number
              p_status?: string
              p_time_from?: string
              p_time_to?: string
            }
          | {
              p_at_date?: string
              p_at_time?: string
              p_category_ids?: string[]
              p_category_names?: string[]
              p_city?: string
              p_date_from?: string
              p_date_to?: string
              p_deal_id?: string
              p_min_seats?: number
              p_owner_id?: string
              p_price_max?: number
              p_price_min?: number
              p_status?: string
              p_time_from?: string
              p_time_to?: string
            }
        Returns: {
          auto_confirm: boolean
          available_seats: number
          busineses_score: number
          business_address: string
          business_city: string
          business_country: string
          business_id: string
          business_name: string
          business_owner_id: string
          business_position_latitude: number
          business_position_longitude: number
          business_state: string
          business_zip_code: string
          category_id: string
          category_name: string
          created_at: string
          deal_categories: Json
          deal_category_names: string[]
          dealid: string
          description: string
          discount_percentage: number
          discounted_price: number
          effective_price: number
          end_date: string
          end_time: string
          fake: boolean
          images: string[]
          original_price: number
          slot_date: string
          start_date: string
          start_time: string
          status: string
          terms_conditions: string
          time_slots: Json
          title: string
          updated_at: string
        }[]
      }
      delete_review_with_business_update: {
        Args: { review_id: string }
        Returns: Json
      }
      disablelongtransactions: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      dropgeometrycolumn: {
        Args:
          | {
              catalog_name: string
              column_name: string
              schema_name: string
              table_name: string
            }
          | { column_name: string; schema_name: string; table_name: string }
          | { column_name: string; table_name: string }
        Returns: string
      }
      dropgeometrytable: {
        Args:
          | { catalog_name: string; schema_name: string; table_name: string }
          | { schema_name: string; table_name: string }
          | { table_name: string }
        Returns: string
      }
      enablelongtransactions: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      equals: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geography: {
        Args: { "": string } | { "": unknown }
        Returns: unknown
      }
      geography_analyze: {
        Args: { "": unknown }
        Returns: boolean
      }
      geography_gist_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      geography_gist_decompress: {
        Args: { "": unknown }
        Returns: unknown
      }
      geography_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      geography_send: {
        Args: { "": unknown }
        Returns: string
      }
      geography_spgist_compress_nd: {
        Args: { "": unknown }
        Returns: unknown
      }
      geography_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
      geography_typmod_out: {
        Args: { "": number }
        Returns: unknown
      }
      geometry: {
        Args:
          | { "": string }
          | { "": string }
          | { "": unknown }
          | { "": unknown }
          | { "": unknown }
          | { "": unknown }
          | { "": unknown }
          | { "": unknown }
        Returns: unknown
      }
      geometry_above: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_analyze: {
        Args: { "": unknown }
        Returns: boolean
      }
      geometry_below: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_cmp: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: number
      }
      geometry_contained_3d: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_contains: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_contains_3d: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_distance_box: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: number
      }
      geometry_distance_centroid: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: number
      }
      geometry_eq: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_ge: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_gist_compress_2d: {
        Args: { "": unknown }
        Returns: unknown
      }
      geometry_gist_compress_nd: {
        Args: { "": unknown }
        Returns: unknown
      }
      geometry_gist_decompress_2d: {
        Args: { "": unknown }
        Returns: unknown
      }
      geometry_gist_decompress_nd: {
        Args: { "": unknown }
        Returns: unknown
      }
      geometry_gist_sortsupport_2d: {
        Args: { "": unknown }
        Returns: undefined
      }
      geometry_gt: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_hash: {
        Args: { "": unknown }
        Returns: number
      }
      geometry_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      geometry_le: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_left: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_lt: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      geometry_overabove: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_overbelow: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_overlaps: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_overlaps_3d: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_overleft: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_overright: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_recv: {
        Args: { "": unknown }
        Returns: unknown
      }
      geometry_right: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_same: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_same_3d: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometry_send: {
        Args: { "": unknown }
        Returns: string
      }
      geometry_sortsupport: {
        Args: { "": unknown }
        Returns: undefined
      }
      geometry_spgist_compress_2d: {
        Args: { "": unknown }
        Returns: unknown
      }
      geometry_spgist_compress_3d: {
        Args: { "": unknown }
        Returns: unknown
      }
      geometry_spgist_compress_nd: {
        Args: { "": unknown }
        Returns: unknown
      }
      geometry_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
      geometry_typmod_out: {
        Args: { "": number }
        Returns: unknown
      }
      geometry_within: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      geometrytype: {
        Args: { "": unknown } | { "": unknown }
        Returns: string
      }
      geomfromewkb: {
        Args: { "": string }
        Returns: unknown
      }
      geomfromewkt: {
        Args: { "": string }
        Returns: unknown
      }
      get_available_businesses: {
        Args: {
          p_category_id?: string
          p_cursor?: string
          p_date: string
          p_lat?: number
          p_limit?: number
          p_lng?: number
          p_radius?: number
          p_time?: string
        }
        Returns: {
          address: string
          available_slots: Json
          business_id: string
          business_name: string
          category_id: string
          distance: number
          end_date: string
          latitude: number
          longitude: number
          next_cursor: string
          photos: string[]
          start_date: string
        }[]
      }
      get_nearby_deals: {
        Args: {
          p_category_id?: string
          p_cursor?: string
          p_date: string
          p_lat?: number
          p_limit?: number
          p_lng?: number
          p_radius?: number
          p_time?: string
        }
        Returns: {
          address: string
          available_slots: Json
          business_id: string
          business_name: string
          category_id: string
          city: string
          country: string
          deal_id: string
          description: string
          discount_percentage: number
          discounted_price: number
          distance: number
          end_date: string
          images: string[]
          latitude: number
          longitude: number
          next_cursor: string
          original_price: number
          start_date: string
          state: string
          title: string
          zip_code: string
        }[]
      }
      get_nearest_businesses_postgis: {
        Args:
          | {
              max_distance_km?: number
              max_results?: number
              require_deals?: boolean
              user_lat: number
              user_lon: number
            }
          | {
              max_distance_km?: number
              max_results?: number
              user_lat: number
              user_lon: number
            }
        Returns: {
          address: string
          deal_count: number
          distance: number
          id: string
          latitude: number
          longitude: number
          name: string
        }[]
      }
      get_proj4_from_srid: {
        Args: { "": number }
        Returns: string
      }
      get_user_highest_tier: {
        Args: { user_id: string }
        Returns: Database["public"]["Enums"]["pricing_tier_type"]
      }
      get_user_role: {
        Args: { user_id: string }
        Returns: string
      }
      gettransactionid: {
        Args: Record<PropertyKey, never>
        Returns: unknown
      }
      gidx_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gidx_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      halfvec_avg: {
        Args: { "": number[] }
        Returns: unknown
      }
      halfvec_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      halfvec_send: {
        Args: { "": unknown }
        Returns: string
      }
      halfvec_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
      hnsw_bit_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnsw_halfvec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnsw_sparsevec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnswhandler: {
        Args: { "": unknown }
        Returns: unknown
      }
      import_leads_to_businesses: {
        Args:
          | { category_id: string; owner_id: string }
          | {
              owner_id: string
              source_category_name: string
              target_category_id: string
            }
          | {
              owner_id: string
              source_category_names: string[]
              target_category_id: string
            }
        Returns: number
      }
      ivfflat_bit_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflat_halfvec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflathandler: {
        Args: { "": unknown }
        Returns: unknown
      }
      json: {
        Args: { "": unknown }
        Returns: Json
      }
      jsonb: {
        Args: { "": unknown }
        Returns: Json
      }
      l2_norm: {
        Args: { "": unknown } | { "": unknown }
        Returns: number
      }
      l2_normalize: {
        Args: { "": string } | { "": unknown } | { "": unknown }
        Returns: string
      }
      longtransactionsenabled: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      match_documents: {
        Args: { filter?: Json; match_count?: number; query_embedding: string }
        Returns: {
          content: string
          id: number
          metadata: Json
          similarity: number
        }[]
      }
      path: {
        Args: { "": unknown }
        Returns: unknown
      }
      pgis_asflatgeobuf_finalfn: {
        Args: { "": unknown }
        Returns: string
      }
      pgis_asgeobuf_finalfn: {
        Args: { "": unknown }
        Returns: string
      }
      pgis_asmvt_finalfn: {
        Args: { "": unknown }
        Returns: string
      }
      pgis_asmvt_serialfn: {
        Args: { "": unknown }
        Returns: string
      }
      pgis_geometry_clusterintersecting_finalfn: {
        Args: { "": unknown }
        Returns: unknown[]
      }
      pgis_geometry_clusterwithin_finalfn: {
        Args: { "": unknown }
        Returns: unknown[]
      }
      pgis_geometry_collect_finalfn: {
        Args: { "": unknown }
        Returns: unknown
      }
      pgis_geometry_makeline_finalfn: {
        Args: { "": unknown }
        Returns: unknown
      }
      pgis_geometry_polygonize_finalfn: {
        Args: { "": unknown }
        Returns: unknown
      }
      pgis_geometry_union_parallel_finalfn: {
        Args: { "": unknown }
        Returns: unknown
      }
      pgis_geometry_union_parallel_serialfn: {
        Args: { "": unknown }
        Returns: string
      }
      point: {
        Args: { "": unknown }
        Returns: unknown
      }
      polygon: {
        Args: { "": unknown }
        Returns: unknown
      }
      populate_geometry_columns: {
        Args:
          | { tbl_oid: unknown; use_typmod?: boolean }
          | { use_typmod?: boolean }
        Returns: number
      }
      postgis_addbbox: {
        Args: { "": unknown }
        Returns: unknown
      }
      postgis_constraint_dims: {
        Args: { geomcolumn: string; geomschema: string; geomtable: string }
        Returns: number
      }
      postgis_constraint_srid: {
        Args: { geomcolumn: string; geomschema: string; geomtable: string }
        Returns: number
      }
      postgis_constraint_type: {
        Args: { geomcolumn: string; geomschema: string; geomtable: string }
        Returns: string
      }
      postgis_dropbbox: {
        Args: { "": unknown }
        Returns: unknown
      }
      postgis_extensions_upgrade: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      postgis_full_version: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      postgis_geos_noop: {
        Args: { "": unknown }
        Returns: unknown
      }
      postgis_geos_version: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      postgis_getbbox: {
        Args: { "": unknown }
        Returns: unknown
      }
      postgis_hasbbox: {
        Args: { "": unknown }
        Returns: boolean
      }
      postgis_index_supportfn: {
        Args: { "": unknown }
        Returns: unknown
      }
      postgis_lib_build_date: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      postgis_lib_revision: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      postgis_lib_version: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      postgis_libjson_version: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      postgis_liblwgeom_version: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      postgis_libprotobuf_version: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      postgis_libxml_version: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      postgis_noop: {
        Args: { "": unknown }
        Returns: unknown
      }
      postgis_proj_version: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      postgis_scripts_build_date: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      postgis_scripts_installed: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      postgis_scripts_released: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      postgis_svn_version: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      postgis_type_name: {
        Args: {
          coord_dimension: number
          geomname: string
          use_new_name?: boolean
        }
        Returns: string
      }
      postgis_typmod_dims: {
        Args: { "": number }
        Returns: number
      }
      postgis_typmod_srid: {
        Args: { "": number }
        Returns: number
      }
      postgis_typmod_type: {
        Args: { "": number }
        Returns: string
      }
      postgis_version: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      postgis_wagyu_version: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      search_deals_semantic: {
        Args: {
          match_count?: number
          query_embedding: string
          similarity_threshold?: number
        }
        Returns: {
          business_address: string
          business_city: string
          business_latitude: number
          business_longitude: number
          business_name: string
          category_id: string
          deal_id: string
          description: string
          discounted_price: number
          original_price: number
          similarity: number
          title: string
        }[]
      }
      sparsevec_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      sparsevec_send: {
        Args: { "": unknown }
        Returns: string
      }
      sparsevec_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
      spheroid_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      spheroid_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_3dclosestpoint: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: unknown
      }
      st_3ddistance: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: number
      }
      st_3dintersects: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      st_3dlength: {
        Args: { "": unknown }
        Returns: number
      }
      st_3dlongestline: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: unknown
      }
      st_3dmakebox: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: unknown
      }
      st_3dmaxdistance: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: number
      }
      st_3dperimeter: {
        Args: { "": unknown }
        Returns: number
      }
      st_3dshortestline: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: unknown
      }
      st_addpoint: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: unknown
      }
      st_angle: {
        Args:
          | { line1: unknown; line2: unknown }
          | { pt1: unknown; pt2: unknown; pt3: unknown; pt4?: unknown }
        Returns: number
      }
      st_area: {
        Args:
          | { "": string }
          | { "": unknown }
          | { geog: unknown; use_spheroid?: boolean }
        Returns: number
      }
      st_area2d: {
        Args: { "": unknown }
        Returns: number
      }
      st_asbinary: {
        Args: { "": unknown } | { "": unknown }
        Returns: string
      }
      st_asencodedpolyline: {
        Args: { geom: unknown; nprecision?: number }
        Returns: string
      }
      st_asewkb: {
        Args: { "": unknown }
        Returns: string
      }
      st_asewkt: {
        Args: { "": string } | { "": unknown } | { "": unknown }
        Returns: string
      }
      st_asgeojson: {
        Args:
          | { "": string }
          | { geog: unknown; maxdecimaldigits?: number; options?: number }
          | { geom: unknown; maxdecimaldigits?: number; options?: number }
          | {
              geom_column?: string
              maxdecimaldigits?: number
              pretty_bool?: boolean
              r: Record<string, unknown>
            }
        Returns: string
      }
      st_asgml: {
        Args:
          | { "": string }
          | {
              geog: unknown
              id?: string
              maxdecimaldigits?: number
              nprefix?: string
              options?: number
            }
          | {
              geog: unknown
              id?: string
              maxdecimaldigits?: number
              nprefix?: string
              options?: number
              version: number
            }
          | {
              geom: unknown
              id?: string
              maxdecimaldigits?: number
              nprefix?: string
              options?: number
              version: number
            }
          | { geom: unknown; maxdecimaldigits?: number; options?: number }
        Returns: string
      }
      st_ashexewkb: {
        Args: { "": unknown }
        Returns: string
      }
      st_askml: {
        Args:
          | { "": string }
          | { geog: unknown; maxdecimaldigits?: number; nprefix?: string }
          | { geom: unknown; maxdecimaldigits?: number; nprefix?: string }
        Returns: string
      }
      st_aslatlontext: {
        Args: { geom: unknown; tmpl?: string }
        Returns: string
      }
      st_asmarc21: {
        Args: { format?: string; geom: unknown }
        Returns: string
      }
      st_asmvtgeom: {
        Args: {
          bounds: unknown
          buffer?: number
          clip_geom?: boolean
          extent?: number
          geom: unknown
        }
        Returns: unknown
      }
      st_assvg: {
        Args:
          | { "": string }
          | { geog: unknown; maxdecimaldigits?: number; rel?: number }
          | { geom: unknown; maxdecimaldigits?: number; rel?: number }
        Returns: string
      }
      st_astext: {
        Args: { "": string } | { "": unknown } | { "": unknown }
        Returns: string
      }
      st_astwkb: {
        Args:
          | {
              geom: unknown[]
              ids: number[]
              prec?: number
              prec_m?: number
              prec_z?: number
              with_boxes?: boolean
              with_sizes?: boolean
            }
          | {
              geom: unknown
              prec?: number
              prec_m?: number
              prec_z?: number
              with_boxes?: boolean
              with_sizes?: boolean
            }
        Returns: string
      }
      st_asx3d: {
        Args: { geom: unknown; maxdecimaldigits?: number; options?: number }
        Returns: string
      }
      st_azimuth: {
        Args:
          | { geog1: unknown; geog2: unknown }
          | { geom1: unknown; geom2: unknown }
        Returns: number
      }
      st_boundary: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_boundingdiagonal: {
        Args: { fits?: boolean; geom: unknown }
        Returns: unknown
      }
      st_buffer: {
        Args:
          | { geom: unknown; options?: string; radius: number }
          | { geom: unknown; quadsegs: number; radius: number }
        Returns: unknown
      }
      st_buildarea: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_centroid: {
        Args: { "": string } | { "": unknown }
        Returns: unknown
      }
      st_cleangeometry: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_clipbybox2d: {
        Args: { box: unknown; geom: unknown }
        Returns: unknown
      }
      st_closestpoint: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: unknown
      }
      st_clusterintersecting: {
        Args: { "": unknown[] }
        Returns: unknown[]
      }
      st_collect: {
        Args: { "": unknown[] } | { geom1: unknown; geom2: unknown }
        Returns: unknown
      }
      st_collectionextract: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_collectionhomogenize: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_concavehull: {
        Args: {
          param_allow_holes?: boolean
          param_geom: unknown
          param_pctconvex: number
        }
        Returns: unknown
      }
      st_contains: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      st_containsproperly: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      st_convexhull: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_coorddim: {
        Args: { geometry: unknown }
        Returns: number
      }
      st_coveredby: {
        Args:
          | { geog1: unknown; geog2: unknown }
          | { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      st_covers: {
        Args:
          | { geog1: unknown; geog2: unknown }
          | { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      st_crosses: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      st_curvetoline: {
        Args: { flags?: number; geom: unknown; tol?: number; toltype?: number }
        Returns: unknown
      }
      st_delaunaytriangles: {
        Args: { flags?: number; g1: unknown; tolerance?: number }
        Returns: unknown
      }
      st_difference: {
        Args: { geom1: unknown; geom2: unknown; gridsize?: number }
        Returns: unknown
      }
      st_dimension: {
        Args: { "": unknown }
        Returns: number
      }
      st_disjoint: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      st_distance: {
        Args:
          | { geog1: unknown; geog2: unknown; use_spheroid?: boolean }
          | { geom1: unknown; geom2: unknown }
        Returns: number
      }
      st_distancesphere: {
        Args:
          | { geom1: unknown; geom2: unknown }
          | { geom1: unknown; geom2: unknown; radius: number }
        Returns: number
      }
      st_distancespheroid: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: number
      }
      st_dump: {
        Args: { "": unknown }
        Returns: Database["public"]["CompositeTypes"]["geometry_dump"][]
      }
      st_dumppoints: {
        Args: { "": unknown }
        Returns: Database["public"]["CompositeTypes"]["geometry_dump"][]
      }
      st_dumprings: {
        Args: { "": unknown }
        Returns: Database["public"]["CompositeTypes"]["geometry_dump"][]
      }
      st_dumpsegments: {
        Args: { "": unknown }
        Returns: Database["public"]["CompositeTypes"]["geometry_dump"][]
      }
      st_dwithin: {
        Args: {
          geog1: unknown
          geog2: unknown
          tolerance: number
          use_spheroid?: boolean
        }
        Returns: boolean
      }
      st_endpoint: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_envelope: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_equals: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      st_expand: {
        Args:
          | { box: unknown; dx: number; dy: number }
          | { box: unknown; dx: number; dy: number; dz?: number }
          | { dm?: number; dx: number; dy: number; dz?: number; geom: unknown }
        Returns: unknown
      }
      st_exteriorring: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_flipcoordinates: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_force2d: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_force3d: {
        Args: { geom: unknown; zvalue?: number }
        Returns: unknown
      }
      st_force3dm: {
        Args: { geom: unknown; mvalue?: number }
        Returns: unknown
      }
      st_force3dz: {
        Args: { geom: unknown; zvalue?: number }
        Returns: unknown
      }
      st_force4d: {
        Args: { geom: unknown; mvalue?: number; zvalue?: number }
        Returns: unknown
      }
      st_forcecollection: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_forcecurve: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_forcepolygonccw: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_forcepolygoncw: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_forcerhr: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_forcesfs: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_generatepoints: {
        Args:
          | { area: unknown; npoints: number }
          | { area: unknown; npoints: number; seed: number }
        Returns: unknown
      }
      st_geogfromtext: {
        Args: { "": string }
        Returns: unknown
      }
      st_geogfromwkb: {
        Args: { "": string }
        Returns: unknown
      }
      st_geographyfromtext: {
        Args: { "": string }
        Returns: unknown
      }
      st_geohash: {
        Args:
          | { geog: unknown; maxchars?: number }
          | { geom: unknown; maxchars?: number }
        Returns: string
      }
      st_geomcollfromtext: {
        Args: { "": string }
        Returns: unknown
      }
      st_geomcollfromwkb: {
        Args: { "": string }
        Returns: unknown
      }
      st_geometricmedian: {
        Args: {
          fail_if_not_converged?: boolean
          g: unknown
          max_iter?: number
          tolerance?: number
        }
        Returns: unknown
      }
      st_geometryfromtext: {
        Args: { "": string }
        Returns: unknown
      }
      st_geometrytype: {
        Args: { "": unknown }
        Returns: string
      }
      st_geomfromewkb: {
        Args: { "": string }
        Returns: unknown
      }
      st_geomfromewkt: {
        Args: { "": string }
        Returns: unknown
      }
      st_geomfromgeojson: {
        Args: { "": Json } | { "": Json } | { "": string }
        Returns: unknown
      }
      st_geomfromgml: {
        Args: { "": string }
        Returns: unknown
      }
      st_geomfromkml: {
        Args: { "": string }
        Returns: unknown
      }
      st_geomfrommarc21: {
        Args: { marc21xml: string }
        Returns: unknown
      }
      st_geomfromtext: {
        Args: { "": string }
        Returns: unknown
      }
      st_geomfromtwkb: {
        Args: { "": string }
        Returns: unknown
      }
      st_geomfromwkb: {
        Args: { "": string }
        Returns: unknown
      }
      st_gmltosql: {
        Args: { "": string }
        Returns: unknown
      }
      st_hasarc: {
        Args: { geometry: unknown }
        Returns: boolean
      }
      st_hausdorffdistance: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: number
      }
      st_hexagon: {
        Args: { cell_i: number; cell_j: number; origin?: unknown; size: number }
        Returns: unknown
      }
      st_hexagongrid: {
        Args: { bounds: unknown; size: number }
        Returns: Record<string, unknown>[]
      }
      st_interpolatepoint: {
        Args: { line: unknown; point: unknown }
        Returns: number
      }
      st_intersection: {
        Args: { geom1: unknown; geom2: unknown; gridsize?: number }
        Returns: unknown
      }
      st_intersects: {
        Args:
          | { geog1: unknown; geog2: unknown }
          | { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      st_isclosed: {
        Args: { "": unknown }
        Returns: boolean
      }
      st_iscollection: {
        Args: { "": unknown }
        Returns: boolean
      }
      st_isempty: {
        Args: { "": unknown }
        Returns: boolean
      }
      st_ispolygonccw: {
        Args: { "": unknown }
        Returns: boolean
      }
      st_ispolygoncw: {
        Args: { "": unknown }
        Returns: boolean
      }
      st_isring: {
        Args: { "": unknown }
        Returns: boolean
      }
      st_issimple: {
        Args: { "": unknown }
        Returns: boolean
      }
      st_isvalid: {
        Args: { "": unknown }
        Returns: boolean
      }
      st_isvaliddetail: {
        Args: { flags?: number; geom: unknown }
        Returns: Database["public"]["CompositeTypes"]["valid_detail"]
      }
      st_isvalidreason: {
        Args: { "": unknown }
        Returns: string
      }
      st_isvalidtrajectory: {
        Args: { "": unknown }
        Returns: boolean
      }
      st_length: {
        Args:
          | { "": string }
          | { "": unknown }
          | { geog: unknown; use_spheroid?: boolean }
        Returns: number
      }
      st_length2d: {
        Args: { "": unknown }
        Returns: number
      }
      st_letters: {
        Args: { font?: Json; letters: string }
        Returns: unknown
      }
      st_linecrossingdirection: {
        Args: { line1: unknown; line2: unknown }
        Returns: number
      }
      st_linefromencodedpolyline: {
        Args: { nprecision?: number; txtin: string }
        Returns: unknown
      }
      st_linefrommultipoint: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_linefromtext: {
        Args: { "": string }
        Returns: unknown
      }
      st_linefromwkb: {
        Args: { "": string }
        Returns: unknown
      }
      st_linelocatepoint: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: number
      }
      st_linemerge: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_linestringfromwkb: {
        Args: { "": string }
        Returns: unknown
      }
      st_linetocurve: {
        Args: { geometry: unknown }
        Returns: unknown
      }
      st_locatealong: {
        Args: { geometry: unknown; leftrightoffset?: number; measure: number }
        Returns: unknown
      }
      st_locatebetween: {
        Args: {
          frommeasure: number
          geometry: unknown
          leftrightoffset?: number
          tomeasure: number
        }
        Returns: unknown
      }
      st_locatebetweenelevations: {
        Args: { fromelevation: number; geometry: unknown; toelevation: number }
        Returns: unknown
      }
      st_longestline: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: unknown
      }
      st_m: {
        Args: { "": unknown }
        Returns: number
      }
      st_makebox2d: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: unknown
      }
      st_makeline: {
        Args: { "": unknown[] } | { geom1: unknown; geom2: unknown }
        Returns: unknown
      }
      st_makepolygon: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_makevalid: {
        Args: { "": unknown } | { geom: unknown; params: string }
        Returns: unknown
      }
      st_maxdistance: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: number
      }
      st_maximuminscribedcircle: {
        Args: { "": unknown }
        Returns: Record<string, unknown>
      }
      st_memsize: {
        Args: { "": unknown }
        Returns: number
      }
      st_minimumboundingcircle: {
        Args: { inputgeom: unknown; segs_per_quarter?: number }
        Returns: unknown
      }
      st_minimumboundingradius: {
        Args: { "": unknown }
        Returns: Record<string, unknown>
      }
      st_minimumclearance: {
        Args: { "": unknown }
        Returns: number
      }
      st_minimumclearanceline: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_mlinefromtext: {
        Args: { "": string }
        Returns: unknown
      }
      st_mlinefromwkb: {
        Args: { "": string }
        Returns: unknown
      }
      st_mpointfromtext: {
        Args: { "": string }
        Returns: unknown
      }
      st_mpointfromwkb: {
        Args: { "": string }
        Returns: unknown
      }
      st_mpolyfromtext: {
        Args: { "": string }
        Returns: unknown
      }
      st_mpolyfromwkb: {
        Args: { "": string }
        Returns: unknown
      }
      st_multi: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_multilinefromwkb: {
        Args: { "": string }
        Returns: unknown
      }
      st_multilinestringfromtext: {
        Args: { "": string }
        Returns: unknown
      }
      st_multipointfromtext: {
        Args: { "": string }
        Returns: unknown
      }
      st_multipointfromwkb: {
        Args: { "": string }
        Returns: unknown
      }
      st_multipolyfromwkb: {
        Args: { "": string }
        Returns: unknown
      }
      st_multipolygonfromtext: {
        Args: { "": string }
        Returns: unknown
      }
      st_ndims: {
        Args: { "": unknown }
        Returns: number
      }
      st_node: {
        Args: { g: unknown }
        Returns: unknown
      }
      st_normalize: {
        Args: { geom: unknown }
        Returns: unknown
      }
      st_npoints: {
        Args: { "": unknown }
        Returns: number
      }
      st_nrings: {
        Args: { "": unknown }
        Returns: number
      }
      st_numgeometries: {
        Args: { "": unknown }
        Returns: number
      }
      st_numinteriorring: {
        Args: { "": unknown }
        Returns: number
      }
      st_numinteriorrings: {
        Args: { "": unknown }
        Returns: number
      }
      st_numpatches: {
        Args: { "": unknown }
        Returns: number
      }
      st_numpoints: {
        Args: { "": unknown }
        Returns: number
      }
      st_offsetcurve: {
        Args: { distance: number; line: unknown; params?: string }
        Returns: unknown
      }
      st_orderingequals: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      st_orientedenvelope: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_overlaps: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      st_perimeter: {
        Args: { "": unknown } | { geog: unknown; use_spheroid?: boolean }
        Returns: number
      }
      st_perimeter2d: {
        Args: { "": unknown }
        Returns: number
      }
      st_pointfromtext: {
        Args: { "": string }
        Returns: unknown
      }
      st_pointfromwkb: {
        Args: { "": string }
        Returns: unknown
      }
      st_pointm: {
        Args: {
          mcoordinate: number
          srid?: number
          xcoordinate: number
          ycoordinate: number
        }
        Returns: unknown
      }
      st_pointonsurface: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_points: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_pointz: {
        Args: {
          srid?: number
          xcoordinate: number
          ycoordinate: number
          zcoordinate: number
        }
        Returns: unknown
      }
      st_pointzm: {
        Args: {
          mcoordinate: number
          srid?: number
          xcoordinate: number
          ycoordinate: number
          zcoordinate: number
        }
        Returns: unknown
      }
      st_polyfromtext: {
        Args: { "": string }
        Returns: unknown
      }
      st_polyfromwkb: {
        Args: { "": string }
        Returns: unknown
      }
      st_polygonfromtext: {
        Args: { "": string }
        Returns: unknown
      }
      st_polygonfromwkb: {
        Args: { "": string }
        Returns: unknown
      }
      st_polygonize: {
        Args: { "": unknown[] }
        Returns: unknown
      }
      st_project: {
        Args: { azimuth: number; distance: number; geog: unknown }
        Returns: unknown
      }
      st_quantizecoordinates: {
        Args: {
          g: unknown
          prec_m?: number
          prec_x: number
          prec_y?: number
          prec_z?: number
        }
        Returns: unknown
      }
      st_reduceprecision: {
        Args: { geom: unknown; gridsize: number }
        Returns: unknown
      }
      st_relate: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: string
      }
      st_removerepeatedpoints: {
        Args: { geom: unknown; tolerance?: number }
        Returns: unknown
      }
      st_reverse: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_segmentize: {
        Args: { geog: unknown; max_segment_length: number }
        Returns: unknown
      }
      st_setsrid: {
        Args: { geog: unknown; srid: number } | { geom: unknown; srid: number }
        Returns: unknown
      }
      st_sharedpaths: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: unknown
      }
      st_shiftlongitude: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_shortestline: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: unknown
      }
      st_simplifypolygonhull: {
        Args: { geom: unknown; is_outer?: boolean; vertex_fraction: number }
        Returns: unknown
      }
      st_split: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: unknown
      }
      st_square: {
        Args: { cell_i: number; cell_j: number; origin?: unknown; size: number }
        Returns: unknown
      }
      st_squaregrid: {
        Args: { bounds: unknown; size: number }
        Returns: Record<string, unknown>[]
      }
      st_srid: {
        Args: { geog: unknown } | { geom: unknown }
        Returns: number
      }
      st_startpoint: {
        Args: { "": unknown }
        Returns: unknown
      }
      st_subdivide: {
        Args: { geom: unknown; gridsize?: number; maxvertices?: number }
        Returns: unknown[]
      }
      st_summary: {
        Args: { "": unknown } | { "": unknown }
        Returns: string
      }
      st_swapordinates: {
        Args: { geom: unknown; ords: unknown }
        Returns: unknown
      }
      st_symdifference: {
        Args: { geom1: unknown; geom2: unknown; gridsize?: number }
        Returns: unknown
      }
      st_symmetricdifference: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: unknown
      }
      st_tileenvelope: {
        Args: {
          bounds?: unknown
          margin?: number
          x: number
          y: number
          zoom: number
        }
        Returns: unknown
      }
      st_touches: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      st_transform: {
        Args:
          | { from_proj: string; geom: unknown; to_proj: string }
          | { from_proj: string; geom: unknown; to_srid: number }
          | { geom: unknown; to_proj: string }
        Returns: unknown
      }
      st_triangulatepolygon: {
        Args: { g1: unknown }
        Returns: unknown
      }
      st_union: {
        Args:
          | { "": unknown[] }
          | { geom1: unknown; geom2: unknown }
          | { geom1: unknown; geom2: unknown; gridsize: number }
        Returns: unknown
      }
      st_voronoilines: {
        Args: { extend_to?: unknown; g1: unknown; tolerance?: number }
        Returns: unknown
      }
      st_voronoipolygons: {
        Args: { extend_to?: unknown; g1: unknown; tolerance?: number }
        Returns: unknown
      }
      st_within: {
        Args: { geom1: unknown; geom2: unknown }
        Returns: boolean
      }
      st_wkbtosql: {
        Args: { wkb: string }
        Returns: unknown
      }
      st_wkttosql: {
        Args: { "": string }
        Returns: unknown
      }
      st_wrapx: {
        Args: { geom: unknown; move: number; wrap: number }
        Returns: unknown
      }
      st_x: {
        Args: { "": unknown }
        Returns: number
      }
      st_xmax: {
        Args: { "": unknown }
        Returns: number
      }
      st_xmin: {
        Args: { "": unknown }
        Returns: number
      }
      st_y: {
        Args: { "": unknown }
        Returns: number
      }
      st_ymax: {
        Args: { "": unknown }
        Returns: number
      }
      st_ymin: {
        Args: { "": unknown }
        Returns: number
      }
      st_z: {
        Args: { "": unknown }
        Returns: number
      }
      st_zmax: {
        Args: { "": unknown }
        Returns: number
      }
      st_zmflag: {
        Args: { "": unknown }
        Returns: number
      }
      st_zmin: {
        Args: { "": unknown }
        Returns: number
      }
      text: {
        Args: { "": unknown }
        Returns: string
      }
      unlockrows: {
        Args: { "": string }
        Returns: number
      }
      update_user_metadata: {
        Args: { new_metadata: Json; target_user_id: string }
        Returns: undefined
      }
      updategeometrysrid: {
        Args: {
          catalogn_name: string
          column_name: string
          new_srid_in: number
          schema_name: string
          table_name: string
        }
        Returns: string
      }
      vector_avg: {
        Args: { "": number[] }
        Returns: string
      }
      vector_dims: {
        Args: { "": string } | { "": unknown }
        Returns: number
      }
      vector_norm: {
        Args: { "": string }
        Returns: number
      }
      vector_out: {
        Args: { "": string }
        Returns: unknown
      }
      vector_send: {
        Args: { "": string }
        Returns: string
      }
      vector_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
    }
    Enums: {
      agent_type:
        | "booking"
        | "customer_support"
        | "sales"
        | "marketing"
        | "data_analysis"
      agent_type_enum:
        | "router"
        | "booking"
        | "discovery"
        | "support"
        | "business_intelligence"
        | "context_manager"
      business_subscription: "freemiun" | "basic" | "medium" | "large"
      conversation_role: "owner" | "customer" | "participant"
      conversation_type:
        | "booking"
        | "chat"
        | "personal_ai_assistant_chat"
        | "business_chat"
        | "deal_chat"
      deal_status_enum: "draft" | "published" | "expired"
      global_setting_type: "string" | "number" | "boolean"
      notification_method_enum: "push" | "email" | "sms" | "in_app"
      pricing_tier_type:
        | "basic"
        | "professional"
        | "enterprise"
        | "personalizzato"
      reminder_status_enum: "pending" | "sent" | "cancelled" | "failed"
      reminder_type_enum: "appointment" | "task" | "booking" | "custom"
      review_sentiment:
        | "very_negative"
        | "negative"
        | "neutral"
        | "positive"
        | "very_positive"
        | "mixed"
      task_priority_enum: "low" | "medium" | "high"
      task_status_enum:
        | "todo"
        | "in_progress"
        | "review"
        | "completed"
        | "cancelled"
    }
    CompositeTypes: {
      geometry_dump: {
        path: number[] | null
        geom: unknown | null
      }
      valid_detail: {
        valid: boolean | null
        reason: string | null
        location: unknown | null
      }
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      agent_type: [
        "booking",
        "customer_support",
        "sales",
        "marketing",
        "data_analysis",
      ],
      agent_type_enum: [
        "router",
        "booking",
        "discovery",
        "support",
        "business_intelligence",
        "context_manager",
      ],
      business_subscription: ["freemiun", "basic", "medium", "large"],
      conversation_role: ["owner", "customer", "participant"],
      conversation_type: [
        "booking",
        "chat",
        "personal_ai_assistant_chat",
        "business_chat",
        "deal_chat",
      ],
      deal_status_enum: ["draft", "published", "expired"],
      global_setting_type: ["string", "number", "boolean"],
      notification_method_enum: ["push", "email", "sms", "in_app"],
      pricing_tier_type: [
        "basic",
        "professional",
        "enterprise",
        "personalizzato",
      ],
      reminder_status_enum: ["pending", "sent", "cancelled", "failed"],
      reminder_type_enum: ["appointment", "task", "booking", "custom"],
      review_sentiment: [
        "very_negative",
        "negative",
        "neutral",
        "positive",
        "very_positive",
        "mixed",
      ],
      task_priority_enum: ["low", "medium", "high"],
      task_status_enum: [
        "todo",
        "in_progress",
        "review",
        "completed",
        "cancelled",
      ],
    },
  },
} as const
