import { useState, useRef, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  Send,
  Mic,
  Square,
  Play,
  Pause,
  User,
  Building2,
} from "lucide-react";
import { useMessages } from "@/hooks/chat/useMessages";
import { useConversations } from "@/hooks/messages/useConversations";
import { useAuth } from "@/hooks/auth/useAuth";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import ReactMarkdown from "react-markdown";
import {
  AI_ASSISTANT_CHAT_AGENT_WEBHOOK_URL,
  BOOKING_CHAT_AGENT_WEBHOOK_URL,
} from "@/data/n8nWebHookUrl";
import VoiceRecordingFeedback from "@/components/chat/VoiceRecordingFeedback";
import UserMessage from "@/components/chat/UserMessage";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useLocationManagement } from "@/hooks/location/useLocationManagement";

import AssistantMessage from "@/components/chat/AssistantMessage";
import { ConversationRole } from "@/services/chatService";
import { CATCH_UP_USER_ID } from "@/data/catchup";
import { useGlobalSetting } from "@/features/global_settings/hook/useGlobalSettings";
import { useQuery } from "@tanstack/react-query";
import { getAiAssistantProfilesByIdQueryOptions } from "@/queryOptions/getAiAssistantProfilesQueryOptions";

type ConversationMessage = {
  id: string;
  role: ConversationRole;
  isLoading: boolean;
  content: string;
  created_at: string;
};

const ConversationDetails = () => {
  const { conversationId } = useParams();
  const navigate = useNavigate();
  const { user, userDetails } = useAuth();
  const { messages, isLoading, conversation } = useMessages(
    conversationId || ""
  );

  const { saveMessageToDB } = useConversations();
  const [newMessage, setNewMessage] = useState("");
  const [isWaiting, setIsWaiting] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  // const [threadId] = useState(() => uuidv4());
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const { userLocation } = useLocationManagement();
  const { data: aiMode } = useGlobalSetting("ai_server");


  const { data: assistantDetails, isLoading: isLoadingAssistant } =  useQuery(getAiAssistantProfilesByIdQueryOptions(userDetails?.selected_assistant_id));
  
  if (!conversationId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-700 mb-2">
            Conversazione non trovata
          </h2>
          <p className="text-gray-500">
            L'ID della conversazione non è valido.
          </p>
          <button
            onClick={() => navigate("/conversations")}
            className="mt-4 px-4 py-2 bg-brand-primary text-white rounded-lg hover:bg-brand-primary/90"
          >
            Torna indietro
          </button>
        </div>
      </div>
    );
  }

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      chunksRef.current = [];

      mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunksRef.current.push(e.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(chunksRef.current, { type: "audio/webm" });
        const reader = new FileReader();
        reader.readAsDataURL(audioBlob);
        reader.onloadend = async () => {
          const base64Audio = reader.result as string;
          const base64Only = base64Audio.split(",")[1];
          await handleSendMessage(base64Only, true);
        };

        stream.getTracks().forEach((track) => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
    } catch (e) {
      console.error(e);
      alert("Per favore, concedi l'accesso al microfono");
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const handleRecord = async () => {
    if (isRecording) {
      stopRecording();
    } else {
      await startRecording();
    }
  };
  const isPersonalAssistant =
    conversation?.type === "personal_ai_assistant_chat";
  const handleSendMessage = async (content: string, isVoice = false) => {
    if ((!content.trim() && !isVoice) || !conversationId || !conversation)
      return;

    const messageText = content.trim();
    setNewMessage("");

    // User Message
    // The exclamation mark (!) is a TypeScript non-null assertion operator
    // It tells TypeScript that even though conversationId might be null or undefined according to its type,
    // we as developers are certain that it will have a value at runtime when this code executes
    // This is used here because we've already checked that conversationId exists in the condition above

    await saveMessageToDB({
      conversationId: conversationId!,
      senderId: user.id,
      content: messageText,
    });

    // Check if the message is from the business owner, then do not send to AI as is a manual response from the business
    const isBusinessOwner = user.id === conversation.owner_id;

    if (isBusinessOwner) {
      return;
    }

    setIsWaiting(true);
    try {
      const request: { url: string; payload: any; senderId: string } =
        isPersonalAssistant
          ? {
              url: AI_ASSISTANT_CHAT_AGENT_WEBHOOK_URL,
              payload: {
                latitude: userLocation?.lat,
                longitude: userLocation?.lng,
                user_query: messageText,
                user_id: user?.id,
                session_id: conversationId!,
                memory_lenght: 15,
                mode: aiMode,
                email_address: user?.email,
                mobile_number: user?.phone,
                isVoiceMessage: isVoice,
              },
              senderId: CATCH_UP_USER_ID,
            }
          : {
              url: BOOKING_CHAT_AGENT_WEBHOOK_URL,
              payload: {
                userinput: messageText,
                conversationId: conversationId!,
                userId: user?.id,
                businessId: conversation.business_id,
                language: "it-IT",
                isVoiceMessage: isVoice,
                userName: userDetails?.first_name,
                latitude: userLocation?.lat,
                longitude: userLocation?.lng,
                //mode: aiMode,
              },
              senderId: conversation.owner_id,
            };

      const response = await fetch(request.url, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(request.payload),
      });

      if (!response.ok) {
        await saveMessageToDB({
          conversationId: conversationId!,
          senderId: request.senderId,
          content:
            "Mi dispiace, c'è stato un errore nella generazione della risposta. (response.ok)",
        });
        throw new Error("Errore nella risposta del webhook");
      }

      const dataResponse = await response.json();
      console.log("Data response:", dataResponse);
      // Some webhooks return an already-parsed object in `response.output`,
      // others return a JSON string. Handle both safely.
      const output = (dataResponse?.response?.output ??
        dataResponse?.output ??
        dataResponse) as unknown;
      const data =
        typeof output === "string" ? JSON.parse(output) : (output as any);

      // Extract metadata
      let metadata = {};
      if (data.llmResponseIntent === "available_deals") {
        metadata = {
          ui: "available_deals",
          deals: data.dealIds,
        };
      }
      if (data.llmResponseIntent === "booking_data") {
        metadata = {
          ui: "booking_data",
          bookingId: data.bookingId,
        };
      }

      // Agent response
      await saveMessageToDB({
        metadata,
        conversationId: conversationId!,
        senderId: request.senderId,
        content: data.llmResponse || "Non ho capito cosa intendi.",
      });
    } catch (error) {
      console.error("Error calling webhook:", error);
      await saveMessageToDB({
        conversationId: conversationId!,
        senderId: isPersonalAssistant
          ? CATCH_UP_USER_ID
          : conversation.owner_id,
        content:
          "Mi dispiace, c'è stato un errore nella generazione della risposta. Riprova a formulare la tua richiesta.",
      });
    } finally {
      setIsWaiting(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(newMessage);
    }
  };

  /**
   * Checks if a string is a valid base64 audio content
   * @param content - The string to check
   * @returns boolean - True if the content is likely a base64 encoded audio, false otherwise
   */
  const isBase64Audio = (content: string) => {
    try {
      // Check if the content is a regular text message
      if (
        content.trim().length === 0 ||
        /^[\p{L}\p{N}\p{P}\p{Z}]+$/u.test(content)
      ) {
        return false;
      }

      // Check if it's a valid base64 string (should only contain valid base64 characters)
      if (!/^[A-Za-z0-9+/=]+$/.test(content)) {
        return false;
      }

      // Additional check for minimum length of reasonable audio data
      if (content.length < 100) {
        return false;
      }

      // Try to decode it
      const decodedString = atob(content);
      return decodedString.length > 0;
    } catch {
      return false;
    }
  };

  const AudioMessage = ({ content }: { content: string }) => {
    const [isPlaying, setIsPlaying] = useState(false);
    const audioRef = useRef<HTMLAudioElement>(null);

    const togglePlay = () => {
      if (audioRef.current) {
        if (isPlaying) {
          audioRef.current.pause();
        } else {
          audioRef.current.play();
        }
      }
    };

    useEffect(() => {
      const audio = audioRef.current;
      if (audio) {
        audio.onplay = () => setIsPlaying(true);
        audio.onpause = () => setIsPlaying(false);
        audio.onended = () => setIsPlaying(false);
      }
    }, []);

    return (
      <div className="flex items-center gap-2">
        <button
          onClick={togglePlay}
          className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
        >
          {isPlaying ? (
            <Pause className="h-4 w-4" />
          ) : (
            <Play className="h-4 w-4" />
          )}
        </button>
        <audio ref={audioRef} src={`data:audio/webm;base64,${content}`} />
        <span className="text-sm">Messaggio vocale</span>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-pulse">Caricamento...</div>
      </div>
    );
  }

  // Ottiene il nome dell'attività dalla conversazione
  const businessName = conversation
    ? conversation.business_name || "Attività"
    : "Conversazione";

  const MessageInput = (
    <footer className="fixed bottom-0 left-0 right-0 bg-white border-t p-4">
      <div className="flex items-center gap-2">
        <textarea
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Scrivi un messaggio..."
          className="flex-1 resize-none border rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-brand-primary"
          rows={1}
        />
        <Button
          onClick={handleRecord}
          variant="outline"
          size="icon"
          className={`rounded-full ${
            isRecording ? "bg-red-500 text-white hover:bg-red-600" : ""
          }`}
        >
          {isRecording ? (
            <Square className="h-5 w-5" />
          ) : (
            <Mic className="h-5 w-5" />
          )}
        </Button>
        <Button
          onClick={() => handleSendMessage(newMessage)}
          disabled={!newMessage.trim() || isWaiting}
          variant="default"
          size="icon"
          className="rounded-full"
        >
          <Send className="h-5 w-5" />
        </Button>
      </div>
    </footer>
  );

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <header className="fixed top-0 left-0 right-0 bg-white z-50 px-4 py-3 flex items-center justify-between border-b">
        <div className="flex items-center">
          <button onClick={() => navigate(-1)} className="p-2">
            <ArrowLeft className="h-5 w-5 text-gray-700" />
          </button>

          {isPersonalAssistant ? (
            <>
              <h1 className="ml-2 text-lg font-semibold mr-4">
                {" "}
                {assistantDetails?.name}
              </h1>
              <Avatar className="h-8 w-8 bg-indigo-500 text-white">
                <AvatarImage src={assistantDetails?.image_url || undefined} />
                <AvatarFallback>
                  <Building2 className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
            </>
          ) : (
            <h1 className="ml-2 text-lg font-semibold"> {businessName}</h1>
          )}
        </div>
      </header>

      <main className="flex-1 pt-16 pb-20 px-4 overflow-y-auto">
        <div className="space-y-4">
          {Conversation()}
          {isWaiting && AgentThinkingRender()}

          <div ref={messagesEndRef} />
        </div>
      </main>

      <VoiceRecordingFeedback isRecording={isRecording} />

      {MessageInput}
    </div>
  );

  function AgentThinkingRender() {
    return (
      <div className="flex justify-start">
        <div className="flex items-end gap-2">
          <Avatar className="h-8 w-8 bg-indigo-500 text-white">
            <AvatarFallback>
              <Building2 className="h-4 w-4" />
            </AvatarFallback>
          </Avatar>
          <div className="bg-white border rounded-lg p-3 shadow-sm">
            <div className="flex space-x-2">
              <div
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "0ms" }}
              ></div>
              <div
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "200ms" }}
              ></div>
              <div
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "400ms" }}
              ></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  function Conversation() {
    return (
      <>
        {messages.map((message) => {
          const isLoggedUsermessage = message.user_id === user.id;
          const isUserMessage = message.role === "customer";
          const isBusinessMessage = message.role === "owner";

          if (isUserMessage) {
            return (
              <UserMessage
                key={message.id}
                message={message}
                isLoggedUsermessage={isLoggedUsermessage}
                AudioMessage={AudioMessage}
                isBase64Audio={isBase64Audio}
              />
            );
          } else if (isBusinessMessage) {
            return (
              <AssistantMessage
                business_name={conversation.business_name || "Attività"}
                key={message.id}
                message={message}
                isLoggedUsermessage={isLoggedUsermessage}
                AudioMessage={AudioMessage}
                isBase64Audio={isBase64Audio}
              />
            );
          } else {
            // Fallback for other message types (if any)
            return (
              <div key={message.id} className="flex justify-start">
                <div className="flex items-end gap-2 max-w-[80%]">
                  <Avatar className="h-8 w-8 bg-gray-700 text-white">
                    <AvatarFallback>
                      <User className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="rounded-lg p-3 shadow-sm bg-indigo-500 text-white rounded-tl-none">
                    <div className="text-xs mb-1 font-medium text-white/80">
                      {isLoggedUsermessage ? "Tu" : ""}
                    </div>
                    {isBase64Audio(message.content) ? (
                      <AudioMessage content={message.content} />
                    ) : (
                      <div className="text-sm prose prose-sm dark:prose-invert max-w-none">
                        <ReactMarkdown>{message.content}</ReactMarkdown>
                      </div>
                    )}
                    <div className="text-xs mt-1 text-white/70">
                      {format(new Date(message.created_at), "HH:mm", {
                        locale: it,
                      })}
                    </div>
                  </div>
                </div>
              </div>
            );
          }
        })}
      </>
    );
  }
};

export default ConversationDetails;
