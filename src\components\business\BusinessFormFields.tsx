
import { AddressAutocomplete, AddressComponents } from "./AddressAutocomplete";
import { Business } from "@/hooks/useBusinesses";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useCategoryQuery } from "@/queries/useCategoryQuery";
import { SubcategorySelector } from "./SubcategorySelector";

type Category = {
  id: string;
  name: string;
  icon?: string;
  description?: string;
};

interface BusinessFormFieldsProps {
  editForm: Partial<Business> & { subcategories?: string[] };
  onFormChange: (field: keyof Business | 'subcategories', value: any) => void;
}

export const BusinessFormFields = ({
  editForm,
  onFormChange,
}: BusinessFormFieldsProps) => {
  const { data: categories = [] } = useCategoryQuery();



  const handleAddressChange = (addressComponents: AddressComponents) => {
    // console.log('Address changed:', {
    //   formatted_address: addressComponents.formatted_address,
    //   latitude: addressComponents.latitude,
    //   longitude: addressComponents.longitude,
    // });

    // onFormChange('address', addressComponents.formatted_address);
    // const lat = addressComponents.latitude;
    // const lng = addressComponents.longitude;
    // if (lat !== undefined) {
    //   console.log('Setting latitude:', addressComponents.latitude);
    //   onFormChange('latitude', lat);
    // }
    // if (lng !== undefined) {
    //   console.log('Setting longitude:', lng);
    //   onFormChange('longitude', lng);
    // }
    onFormChange('address', addressComponents.route + ", " + addressComponents.street_number);
    onFormChange('latitude', addressComponents.latitude);
    onFormChange('longitude', addressComponents.longitude);
    onFormChange('formatted_address', addressComponents.formatted_address);
    onFormChange('zip_code', addressComponents.postal_code);
    onFormChange('city', addressComponents.locality);
    onFormChange('state', addressComponents.administrative_area_level_2);
    onFormChange('country', addressComponents.country);

  };

  return (
    <>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Nome attività
        </label>
        <input
          type="text"
          value={editForm.name || ''}
          onChange={(e) => onFormChange('name', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Categoria
        </label>
        <Select
          value={editForm.category_id || ''}
          onValueChange={(value) => onFormChange('category_id', value)}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Seleziona una categoria" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Subcategory Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Sottocategorie
        </label>
        <SubcategorySelector
          categoryId={editForm.category_id || null}
          selectedSubcategories={editForm.subcategories || []}
          onSubcategoriesChange={(subcategories) => onFormChange('subcategories', subcategories)}
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Descrizione
        </label>
        <textarea
          value={editForm.description || ''}
          onChange={(e) => onFormChange('description', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary resize-none"
          placeholder="Descrivi la tua attività..."
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Indirizzo
        </label>
        <AddressAutocomplete
          value={editForm.formatted_address || ''}
          onChange={handleAddressChange}
          
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Telefono
        </label>
        <input
          type="tel"
          value={editForm.phone || ''}
          onChange={(e) => onFormChange('phone', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Email
        </label>
        <input
          type="email"
          value={editForm.email || ''}
          onChange={(e) => onFormChange('email', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Sito web
        </label>
        <input
          type="url"
          value={editForm.website || ''}
          onChange={(e) => onFormChange('website', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
        />
      </div>
    </>
  );
};
