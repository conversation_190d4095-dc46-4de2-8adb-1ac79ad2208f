
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import type { Database } from "@/integrations/supabase/types";
import { updateDemoCoordinates as updateLocationServiceCoords } from "@/contexts/LocationContext";
import { DemoLocationCoordinates } from "@/types/location";

// Use the database types directly - they already include all the fields we need
type UserDetails = Database['public']['Tables']['user_details']['Row'];
type UserDetailsUpdate = Database['public']['Tables']['user_details']['Update'];

// Helper type for demo location coordinates


export const useUserDetails = (userId: string | undefined) => {
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);

  useEffect(() => {
    const fetchUserDetails = async () => {
      if (!userId) {
       // console.log('useUserDetails: No userId provided, clearing userDetails');
        setUserDetails(null);
        return;
      }

      //console.log('useUserDetails: Fetching user details for:', userId);
      try {
        const { data: userDetailsData, error: detailsError } = await supabase
          .from('user_details')
          .select(`*`)
          .eq('id', userId)
          .maybeSingle();
        
        if (detailsError) {
          console.error('useUserDetails: Error fetching user details:', detailsError);
          toast.error("Errore nel caricamento dei dettagli utente");
        } else if (userDetailsData) {
          // console.log('useUserDetails: User details fetched successfully:', userDetailsData);
          // console.log('useUserDetails: Demo location from DB:', userDetailsData.demo_location);
          setUserDetails(userDetailsData);
        } else {
       //   console.log('useUserDetails: No user details found for userId:', userId);
          setUserDetails(null);
        }
      } catch (error) {
        console.error('useUserDetails: Error in fetchUserDetails:', error);
      }
    };

    fetchUserDetails();
  }, [userId]);

  const updateLocationEnabled = async (enabled: boolean) => {
    if (!userId) return;
    
    try {
      const updateData: UserDetailsUpdate = { location_enabled: enabled };
      
      const { error } = await supabase
        .from('user_details')
        .update(updateData)
        .eq('id', userId);
      
      if (error) {
        console.error('Error updating location preference:', error);
        toast.error("Errore nell'aggiornamento delle preferenze di posizione");
        return false;
      }
      
      // Update local state
      setUserDetails(prev => prev ? { ...prev, location_enabled: enabled } : null);
      toast.success(`Posizione ${enabled ? 'abilitata' : 'disabilitata'} con successo`);
      return true;
    } catch (error) {
      console.error('Error in updateLocationEnabled:', error);
      toast.error("Si è verificato un errore");
      return false;
    }
  };

  const updateDemoCoordinates = async (latitude: number, longitude: number) => {
    if (!userId) return false;
    console.log('Updating demo coordinates for:', userId);
    console.log('Latitude:', latitude);
    console.log('Longitude:', longitude);
    try {
      const demoLocation: DemoLocationCoordinates = { lat: latitude, lng: longitude };
      const updateData: UserDetailsUpdate = { 
        demo_location: demoLocation as any // Cast to Json type
      };
      
      const { error } = await supabase
        .from('user_details')
        .update(updateData)
        .eq('id', userId);
      
      if (error) {
        console.error('Error updating demo coordinates:', error);
        toast.error("Errore nell'aggiornamento delle coordinate demo");
        return false;
      }
      
      // Update local state
      setUserDetails(prev => prev ? { 
        ...prev, 
        demo_location: demoLocation as any 
      } : null);
      
      // Notify location service about the coordinate update
      console.log('Notifying location service of demo coordinate update');
      updateLocationServiceCoords(demoLocation);
      
      toast.success("Coordinate demo aggiornate con successo");
      return true;
    } catch (error) {
      console.error('Error in updateDemoCoordinates:', error);
      toast.error("Si è verificato un errore");
      return false;
    }
  };

  // Helper function to get demo coordinates from the JSON field
  const getDemoCoordinates = (): DemoLocationCoordinates | null => {
        // console.log('getDemoCoordinates: Called with userDetails:', userDetails);
        // console.log('getDemoCoordinates: demo_location field:', userDetails?.demo_location);
        
    if (!userDetails?.demo_location) {
      console.warn('getDemoCoordinates: No demo_location found, returning null');
      return null;
    }
    
    try {
      const location = userDetails.demo_location;
      // console.log('getDemoCoordinates: Processing location:', location);
      // console.log('getDemoCoordinates: Location type:', typeof location);
      
      // Type guard to check if the location is a valid coordinate object
      if (
        location && 
        typeof location === 'object' && 
        !Array.isArray(location) &&
        location !== null
      ) {
        const locationObj = location as { [key: string]: any };
        
        if (
          'lat' in locationObj && 
          'lng' in locationObj &&
          typeof locationObj.lat === 'number' &&
          typeof locationObj.lng === 'number'
        ) {
          const coords = locationObj as DemoLocationCoordinates;
       //   console.log('getDemoCoordinates: Valid coordinates found:', coords);
          return coords;
        } 
        //else {
          // console.log('getDemoCoordinates: Invalid coordinate structure:', {
          //   hasLat: 'lat' in locationObj,
          //   hasLng: 'lng' in locationObj,
          //   latType: typeof locationObj.lat,
          //   lngType: typeof locationObj.lng
          // }
        // );
        // }
      } else {
        console.warn('getDemoCoordinates: Location is not an object:', typeof location);
      }
    } catch (error) {
      console.error('getDemoCoordinates: Error parsing demo location:', error);
    }
    
    console.warn('getDemoCoordinates: Returning null - invalid or missing coordinates');
    return null;
  };

  const getFullName = () => {
    if (!userDetails) return '';
    const firstName = userDetails.first_name || '';
    const lastName = userDetails.last_name || '';
    return `${firstName} ${lastName}`.trim() || userDetails.display_name || '';
  };

  return {
    userDetails,
    updateLocationEnabled,
    updateDemoCoordinates,
    getDemoCoordinates,
    getFullName
  };
};
