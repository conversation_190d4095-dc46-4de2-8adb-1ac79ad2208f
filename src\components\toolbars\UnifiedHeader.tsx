import { useState, useEffect } from "react";
import {
  ArrowLeft,
  Share2,
  Heart,
  Store,
  MessageCircle,
  SlidersHorizontalIcon,
  LucideFunnel,
  FunnelIcon,
  BellIcon,
} from "lucide-react";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { useAuth } from "@/hooks/auth/useAuth";
import { useNotificationCount } from "@/hooks/useNotificationCount";
import { useNavigate } from "react-router-dom";
import { useDragScroll } from "@/hooks/useDragScroll";
import { getTimeBasedGreetingITA } from "@/utils/dateUtils";

import { Button } from "@/components/ui/button";
import { MenuTab } from "@/pages/mains/topmenu";
import { toast } from "sonner";
import { createOrGetPersonalAIAssistantChatConversation } from "@/services/chatService";
import { useQuery } from "@tanstack/react-query";

import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { getAiAssistantProfilesByIdQueryOptions } from "@/queryOptions/getAiAssistantProfilesQueryOptions";

interface UnifiedHeaderProps {
  // Layout variants
  variant?:
    | "default"
    | "dashboard"
    | "minimal"
    | "business"
    | "deal-details"
    | "booking"
    | "with-tabs";

  // Navigation
  showBackButton?: boolean;
  onBack?: () => void;

  // Content
  title?: string;
  showGreeting?: boolean;
  showDate?: boolean;

  // Business specific
  businessName?: string;
  isBusiness?: boolean;

  // Deal details specific
  isFavorite?: boolean;
  onToggleFavorite?: () => void;
  onShare?: () => void;

  // Filter Category functionality
  showFilters?: boolean;
  onFilterClick?: () => void;
  activeFiltersCount?: number;
  hasActiveFilters?: boolean;

  // Notifications
  showNotifications?: boolean;

  // Tabs (for with-tabs variant)
  tabs?: MenuTab[];
  activeTab?: string;
  onTabChange?: (tabId: string) => void;

  // Visual
  showShadow?: boolean;
  sticky?: boolean;
  background?: "default" | "transparent" | "blur";
}

const UnifiedHeader = ({
  variant = "default",
  showBackButton = false,
  onBack,
  title,
  showGreeting = false,
  showDate = false,
  businessName,
  isBusiness = false,
  isFavorite = false,
  onToggleFavorite,
  onShare,
  showFilters = false,
  onFilterClick,
  activeFiltersCount = 0,
  hasActiveFilters = false,
  showNotifications = true,
  tabs = [],
  activeTab,
  onTabChange,
  showShadow = true,
  sticky = true,
  background = "default",
}: UnifiedHeaderProps) => {
  const { userDetails, isAuthenticated , isLoading: isAuthLoading,
  } = useAuth();
  const { totalCount,isLoadingNotifications } = useNotificationCount();
  const navigate = useNavigate();

  const [isScrolled, setIsScrolled] = useState(false);
  const [hideTabBar, setHideTabBar] = useState(false);

  const tabsDragRef = useDragScroll<HTMLDivElement>();
  const { data: assistantDetails, isLoading: isLoadingAssistant } =  useQuery(getAiAssistantProfilesByIdQueryOptions(userDetails?.selected_assistant_id));

  
 

  // Scroll handling for with-tabs variant
  useEffect(() => {
    if (variant !== "with-tabs") return;

    let lastScrollY = 0;
    let scrollTimeout: NodeJS.Timeout;
    let isAnimating = false;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const documentHeight = document.documentElement.scrollHeight;
      const windowHeight = window.innerHeight;
      const maxScroll = documentHeight - windowHeight;

      if (maxScroll < 200) {
        setHideTabBar(false);
        setIsScrolled(currentScrollY > 10);
        return;
      }

      if (isAnimating) return;

      clearTimeout(scrollTimeout);

      const scrollDifference = currentScrollY - lastScrollY;
      const scrollDirection = scrollDifference > 0 ? "down" : "up";

      const hideThreshold = 120;
      const showThreshold = 80;
      const minScrollMovement = 25;

      if (Math.abs(scrollDifference) > minScrollMovement) {
        if (scrollDirection === "down" && currentScrollY > hideThreshold) {
          if (!hideTabBar) {
            isAnimating = true;
            setHideTabBar(true);
            setTimeout(() => {
              isAnimating = false;
            }, 300);
          }
        } else if (scrollDirection === "up" && currentScrollY < showThreshold) {
          if (hideTabBar) {
            isAnimating = true;
            setHideTabBar(false);
            setTimeout(() => {
              isAnimating = false;
            }, 300);
          }
        }

        lastScrollY = currentScrollY;
      }

      setIsScrolled(currentScrollY > 10);

      scrollTimeout = setTimeout(() => {
        if (currentScrollY <= 60 && hideTabBar) {
          isAnimating = true;
          setHideTabBar(false);
          setTimeout(() => {
            isAnimating = false;
          }, 300);
        }
      }, 150);
    };

    let ticking = false;
    const throttledHandler = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", throttledHandler, { passive: true });

    return () => {
      window.removeEventListener("scroll", throttledHandler);
      clearTimeout(scrollTimeout);
    };
  }, [hideTabBar, variant]);

  const handleNotificationClick = () => {
    navigate("/conversations?tab=notifications");
  };

  const handleBackClick = () => {
    if (onBack) {
      onBack();
      return;
    }

    // Se non c'è una history valida, usa un fallback
    const idx = (window.history && (window.history.state as any)?.idx) ?? 0;
    const canGoBack = idx > 0 || window.history.length > 1;

    if (canGoBack) {
      navigate(-1);
    } else {
      navigate("/", { replace: true });
    }
  };

  const formatDateInItalian = () => {
    const formatted = format(new Date(), "EEEE, d MMMM", { locale: it });
    const parts = formatted.split(", ");
    const weekday = parts[0].charAt(0).toUpperCase() + parts[0].slice(1);
    const dayAndMonth = parts[1].split(" ");
    const day = dayAndMonth[0];
    const month =
      dayAndMonth[1].charAt(0).toUpperCase() + dayAndMonth[1].slice(1);
    return `${weekday}, ${day} ${month}`;
  };

  const firstName = userDetails?.first_name || "Utente";

  // Header base classes
  const baseClasses = `w-full z-50 ${sticky ? "fixed top-0" : "relative"} ${
    background === "blur"
      ? "bg-white/95 backdrop-blur-md"
      : background === "transparent"
      ? "bg-transparent"
      : "bg-white"
  } ${
    showShadow
      ? variant === "with-tabs" && isScrolled
        ? "shadow-lg border-b border-gray-200/50"
        : "shadow-sm"
      : ""
  }`;

  // Content classes based on variant
  const contentClasses =
    variant === "with-tabs"
      ? `transition-all duration-300 ${
          isScrolled ? "shadow-lg border-b border-gray-200/50" : "shadow-sm"
        }`
      : "";

  // Render left section
  const renderLeftSection = () => {
    if (showBackButton) {
      return (
        <Button
          onClick={handleBackClick}
          // className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          variant="ghost"
        >
          <ArrowLeft className="h-5 w-5 text-gray-700" />
        </Button>
      );
    }

    if (showFilters && onFilterClick) {
      return (
        <Button
          className={`relative p-3 rounded-2xl transition-all duration-300 group ${
            hasActiveFilters
              ? // ? "text-primary bg-primary/10 hover:bg-primary/15 shadow-md border border-primary/20"
                "text-primary bg-muted/50 hover:bg-muted shadow-md border border-primary/20"
              : "text-muted-foreground hover:text-foreground bg-muted/50 hover:bg-muted hover:shadow-sm border border-transparent hover:border-border/50"
          }`}
          onClick={onFilterClick}
          aria-label="Apri filtri"
        >
          <FunnelIcon className="h-5 w-5 transition-transform duration-200 group-hover:scale-110" />
          {hasActiveFilters && activeFiltersCount > 0 && (
            <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center font-semibold shadow-lg animate-pulse border-2 border-background">
              {activeFiltersCount > 9 ? "9+" : activeFiltersCount}
            </span>
          )}
        </Button>
      );
    }

    return <div></div>;
  };

  // Render center section
  const renderCenterSection = () => {
    if (variant === "business" && businessName) {
      return (
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 rounded-full bg-brand-primary/10 flex items-center justify-center text-brand-primary">
            {businessName[0].toUpperCase()}
          </div>
          <span className="font-semibold text-gray-800">{businessName}</span>
        </div>
      );
    }

    if (title) {
      return <h1 className="text-xl text-gray-800 font-semibold">{title}</h1>;
    }

    if (showGreeting && isAuthenticated) {
      return (
        <div className="text-center flex-1">
          <div className="text-lg font-semibold text-gray-800">
            {getTimeBasedGreetingITA()} {firstName}
          </div>
          {showDate && (
            <div className="text-xs text-gray-500 font-medium mt-0.5">
              {formatDateInItalian()}
            </div>
          )}
        </div>
      );
    }

    if (!isAuthenticated) {
      return (
        <div className="text-center flex-1">
          <div className="text-2xl font-bold bg-gradient-to-r from-brand-secondary via-brand-primary to-purple-600 bg-clip-text text-transparent">
            CatchUp
          </div>
          {showDate && (
            <div className="text-xs text-gray-500 font-medium mt-0.5">
              {formatDateInItalian()}
            </div>
          )}
        </div>
      );
    }

    return <div className="flex-1"></div>;
  };

  // Render right section
  const renderRightSection = () => {
    const actions = [];

    if (variant === "deal-details") {
      if (onToggleFavorite) {
        actions.push(
          <Button
            key="favorite"
            variant="ghost"
            onClick={onToggleFavorite}
            className="p-2"
          >
            <Heart
              className={`h-5 w-5 ${
                isFavorite ? "text-red-500 fill-current" : "text-gray-700"
              }`}
            />
          </Button>
        );
      }
      if (onShare) {
        actions.push(
          <Button key="share" variant="ghost" onClick={onShare} className="p-2">
            <Share2 className="h-5 w-5 text-gray-700" />
          </Button>
        );
      }
    }

    if (isBusiness) {
      actions.push(
        <Store key="business-icon" className="h-6 w-6 text-brand-primary" />
      );
    }

    if (showNotifications && isAuthenticated) {
      actions.push(
        <Button
          key="notifications"
         // className="text-muted-foreground hover:text-foreground relative p-3 hover:bg-muted rounded-2xl transition-all duration-300 hover:shadow-sm border border-transparent hover:border-border/50 group"
          className={`relative p-3 rounded-2xl transition-all duration-300 group ${
            totalCount > 0
              ? // ? "text-primary bg-primary/10 hover:bg-primary/15 shadow-md border border-primary/20"
                "text-primary bg-muted/50 hover:bg-muted shadow-md border border-primary/20"
              : "text-muted-foreground hover:text-foreground bg-muted/50 hover:bg-muted hover:shadow-sm border border-transparent hover:border-border/50"
          }`}
          onClick={handleNotificationClick}
          aria-label="Apri notifiche"
        >
          <BellIcon className="h-5 w-5 transition-transform duration-200 group-hover:scale-110" />
          {totalCount > 0 && (
            <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center font-semibold shadow-lg animate-pulse border-2 border-background">
              {totalCount > 9 ? "9+" : totalCount}
            </span>
          )}
        </Button>
      );
    }

    return <div className="flex items-center gap-2">{actions}</div>;
  };

  const onTabClick = async (tab: MenuTab) => {
    if (onTabChange) {
      onTabChange(tab.id);
    }
    // Navigate based on tab
    if (tab.href) {
      navigate(tab.href);
      return;
    }
    if (tab.invokeFunction) {
      if (!userDetails || !userDetails.id) {
        toast.error("Utente non autenticato");
        return;
      }
      const result = await createOrGetPersonalAIAssistantChatConversation({
        userId: userDetails.id,
        welcomeMessageData: {
          businessName: "Ai Assistant",
        },
      });

      navigate(`/conversation/${result.conversationId}`);

      return;
    }
    // Guard clause
    navigate("/");
  };

  if(isAuthLoading || isLoadingNotifications) {
    return (
      <div className={baseClasses}>
        <div className="px-4 py-3 flex justify-center items-center">
          <div className="animate-pulse text-sm text-muted-foreground">Caricamento...</div>
        </div>
      </div>
    );
  }

  return (
    <div className={baseClasses}>
      <div className={contentClasses}>
        {/* Main Header */}
        <header
          className={`px-4 py-3 flex justify-between items-center ${
            variant === "with-tabs"
              ? "border-b border-gray-100/60"
              : variant === "deal-details" || variant === "booking"
              ? "border-b"
              : ""
          }`}
        >
          {renderLeftSection()}
          {renderCenterSection()}
          {renderRightSection()}
        </header>

        {/* Tab Bar for with-tabs variant */}
        {variant === "with-tabs" && tabs.length > 0 && (
          <div
            ref={tabsDragRef}
            className={`px-4 overflow-x-auto hide-scrollbar cursor-grab active:cursor-grabbing select-none transition-all duration-500 ease-in-out ${
              hideTabBar
                ? "-translate-y-full opacity-0 max-h-0 py-0"
                : "translate-y-0 opacity-100 max-h-20 py-3"
            }`}
          >
            <div className="flex space-x-2 min-w-max">
              {tabs.map((tab, index) => {
                const isActive = activeTab === tab.id;
                const IconComponent = tab.icon;
                return (
                  <Button
                    key={tab.id}
                    onClick={() => onTabClick?.(tab)}
                    className={`relative px-4 py-3 rounded-2xl font-medium text-sm transition-all duration-300 whitespace-nowrap flex items-center gap-2 group ${
                      isActive
                        ? "bg-primary/50 text-primary-foreground shadow-lg border border-primary/20 transform scale-102"
                        : "bg-muted/50 text-muted-foreground hover:bg-muted hover:text-foreground hover:shadow-md hover:scale-102 border border-transparent hover:border-border/50"
                    }`}
                    style={{
                      animationDelay: `${index * 50}ms`,
                    }}
                  >
                    {tab.label === "AI Assistant" ? (
                      <>
                        <Avatar className="h-8 w-8 bg-indigo-500 text-white">
                          <AvatarImage
                            src={assistantDetails?.image_url || undefined}
                          />
                          <AvatarFallback>
                            <IconComponent
                              className={`h-4 w-4 transition-all duration-300 ${
                                isActive
                                  ? "text-primary-foreground"
                                  : "text-muted-foreground group-hover:text-foreground group-hover:scale-110"
                              }`}
                            />
                          </AvatarFallback>
                        </Avatar>
                        <span className="relative z-10 transition-colors duration-300">
                          Parla con {assistantDetails?.name || tab.label}
                        </span>
                      </>
                    ) : (
                      <>
                        <IconComponent
                          className={`h-4 w-4 transition-all duration-300 ${
                            isActive
                              ? "text-primary-foreground"
                              : "text-muted-foreground group-hover:text-foreground group-hover:scale-110"
                          }`}
                        />
                        <span className="relative z-10 transition-colors duration-300">
                          {tab.label}
                        </span>
                      </>
                    )}

                    {isActive && (
                      <div className="absolute inset-0 bg-primary/10 rounded-2xl animate-pulse" />
                    )}

                    {tab.badge && tab.badge > 0 ? (
                      <span
                        className={`absolute -top-1 -right-1 text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold shadow-lg z-20 border-2 border-background ${
                          isActive
                            ? "bg-primary-foreground text-primary animate-bounce"
                            : "bg-primary text-primary-foreground animate-pulse"
                        }`}
                      >
                        {tab.badge > 9 ? "9+" : tab.badge}
                      </span>
                    ) : null}
                  </Button>
                );
              })}
            </div>

            <div className="mt-2 h-0.5 bg-gradient-to-r from-transparent via-primary/30 to-transparent rounded-full" />
          </div>
        )}
      </div>
    </div>
  );
};

export default UnifiedHeader;
