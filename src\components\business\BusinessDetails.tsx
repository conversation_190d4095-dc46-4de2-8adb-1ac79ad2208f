
import { useState, useEffect } from "react";
import { Business } from "@/hooks/useBusinesses";
import { Badge } from "@/components/ui/badge";
import { supabase } from "@/integrations/supabase/client";

interface BusinessDetailsProps {
  business: Business;
}

export const BusinessDetails = ({ business }: BusinessDetailsProps) => {
  const [subcategoryNames, setSubcategoryNames] = useState<string[]>([]);

  useEffect(() => {
    const fetchSubcategoryNames = async () => {
      if (!business.subcategories || business.subcategories.length === 0) {
        setSubcategoryNames([]);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('categories_sub')
          .select('name')
          .in('id', business.subcategories);

        if (error) {
          console.error('Error fetching subcategory names:', error);
          return;
        }

        setSubcategoryNames(data?.map(sub => sub.name) || []);
      } catch (error) {
        console.error('Error fetching subcategory names:', error);
      }
    };

    fetchSubcategoryNames();
  }, [business.subcategories]);

  return (
    <>
      {business.description && (
        <div className="text-gray-600 mb-4">
          <p>{business.description}</p>
        </div>
      )}
      
      {subcategoryNames.length > 0 && (
        <div className="mb-4">
          <span className="font-medium text-gray-600 mb-2 block">Sottocategorie:</span>
          <div className="flex flex-wrap gap-2">
            {subcategoryNames.map((name, index) => (
              <Badge 
                key={index} 
                variant="secondary" 
                className="text-xs"
              >
                {name}
              </Badge>
            ))}
          </div>
        </div>
      )}

      <div className="flex items-center gap-2 text-gray-600">
        <span className="font-medium">Indirizzo:</span>
        <span>{business.address}</span>
      </div>
      {business.phone && (
        <div className="flex items-center gap-2 text-gray-600">
          <span className="font-medium">Telefono:</span>
          <span>{business.phone}</span>
        </div>
      )}
      {business.email && (
        <div className="flex items-center gap-2 text-gray-600">
          <span className="font-medium">Email:</span>
          <span>{business.email.replace('["', '').replace('"]', '')}</span>
        </div>
      )}
      {business.website && (
        <div className="flex items-center gap-2 text-gray-600">
          <span className="font-medium">Sito web:</span>
          <a 
            href={business.website} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-brand-primary hover:underline"
          >
            {business.website}
          </a>
        </div>
      )}
    </>
  );
};
