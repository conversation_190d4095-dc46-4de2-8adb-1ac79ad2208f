import React from 'react';
import { 
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Badge } from "@/components/ui/badge";
import DealCard from '@/components/deals/core/DealCard';
import SwipeHintIcon from '@/components/ui/SwipeHintIcon';
import { Deal } from '@/types/deals';
import { cn } from '@/lib/utils';
import { useInitialBounceAnimation } from '@/hooks/useInitialBounceAnimation';

interface DealCarouselProps {
  deals: Deal[];
  title: string;
  isLoading?: boolean;
  variant: 'full' | 'compact'; 
  showFavoriteButton?: boolean;
  emptyMessage?: string;
  onDealClick?: (dealId: string) => void;
  className?: string;
  renderDealCard?: (deal: Deal) => React.ReactNode;
  showDetailedTimeSlots?: boolean;
  showNavigationArrows?:boolean;
  hideTimeSlots?: boolean;
  selectedTime?: string;
  selectedDate?: string;
  showVisitBusinessPage: boolean;
}

const DealCarousel: React.FC<DealCarouselProps> = ({
  deals,
  title,
  isLoading = false,
  variant = 'compact',
  showFavoriteButton = false,
  emptyMessage = "Nessuna offerta disponibile",
  onDealClick,
  className,
  renderDealCard,
  showDetailedTimeSlots = false,
  showNavigationArrows = false,
  hideTimeSlots = false,
  selectedTime,
  selectedDate,
  showVisitBusinessPage: showVisitBusiness,
}) => {
  
  const { animationClasses, showSwipeIcon } = useInitialBounceAnimation({
    delay: 1500,
    duration: 800,
    enabled: deals.length > 1 && !isLoading
  });
  
  const handleDealClick = (dealId: string) => {
    if (onDealClick) {
      onDealClick(dealId);
    }
  };

  // Rendering degli skeleton durante il caricamento
  if (isLoading) {
    return (
      <div className={cn("my-4", className)}>
        <h2 className="text-lg font-semibold mb-2">{title}</h2>
        <div className="flex gap-4 overflow-hidden py-2">
          {[1, 2, 3].map((_, index) => (
            <div 
              key={index} 
              className="min-w-[280px] h-64 bg-gray-200 rounded-xl animate-pulse"
            />
          ))}
        </div>
      </div>
    );
  }

  // Messaggio quando non ci sono offerte
  if (deals.length === 0) {
    return (
      <div className={cn("my-4", className)}>
        <h2 className="text-lg font-semibold mb-2">{title}</h2>
        <div className="p-6 text-center bg-gray-50 rounded-xl">
          <p className="text-gray-500">{emptyMessage}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("my-4 relative", className)}>
      <div className="flex items-center gap-2 mb-2">
        <h2 className="text-lg font-semibold">{title}</h2>
        <Badge variant="secondary" className="bg-brand-primary text-white">
          {deals.length}
        </Badge>
      </div>
      
      <div className="relative">
        <Carousel
          opts={{
            align: "start",
            loop: deals.length > 3
          }}
          className={cn("mt-2", animationClasses)}
        >
          <CarouselContent>
            {deals.map((deal) => (
              <CarouselItem 
                key={deal.id} 
                className="md:basis-1/2 lg:basis-1/3"
              >
                <div className="p-1">
                  {renderDealCard ? (
                    renderDealCard(deal)
                  ) : (
                    <DealCard
                      deal={deal}
                      variant={variant}
                      showFavoriteButton={showFavoriteButton}
                      onClick={() => handleDealClick(deal.id)}
                      showDetailedTimeSlots={showDetailedTimeSlots}
                      hideTimeSlots={hideTimeSlots}
                      selectedTime={selectedTime}
                      selectedDate={selectedDate}
                      showVisitBusiness={showVisitBusiness}
                    />
                  )}
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          {showNavigationArrows && deals.length > 1 && (
            <>
              <CarouselPrevious className="left-2 bg-white" />
              <CarouselNext className="right-2 bg-white" />
            </>
          )}
        </Carousel>
        
        <SwipeHintIcon show={showSwipeIcon} />
      </div>
    </div>
  );
};

export default DealCarousel;
